using UnityEngine;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

#if UNITY_IPHONE
public class IAppsTeamIOSUntil : MonoBehaviour {

	[DllImport("__Internal")]
    private static extern bool _iappsteamIOSCheckContentUnLock(string productIdentifier);
	
	[DllImport("__Internal")]
    private static extern string _checkUserOSLanguage();
	
	[DllImport("__Internal")]
    private static extern void _mobClick_beginEvent(string event_name);
	
	[DllImport("__Internal")]
    private static extern void _mobClick_endEvent(string event_name);
	
	[DllImport("__Internal")]
    private static extern void _mobClick_event(string event_name);
    
	[DllImport("__Internal")]
	private static extern void _umeng_updateOnlineConfig();
	
	/*
	[DllImport("__Internal")]
    private static extern bool _is_ipad();
	*/	
	[DllImport("__Internal")]
    private static extern bool _connectedToNetwork();
    
	[DllImport("__Internal")]
	private static extern string _getConfigParams(string key_name);

	
	[DllImport("__Internal")]
    private static extern float _systemVersion();
    
	[DllImport("__Internal")]
	private static extern void _loadOurAdsData(float x, float y, float w, float h);
	
	[DllImport("__Internal")]
	private static extern void _removeOurAds();
	
	[DllImport("__Internal")]
	private static extern void _getLanguageUserFlag(string languageUserFlag);
	
	[DllImport("__Internal")]
	private static extern void _removeOurAdsInterstitial();
	
	[DllImport("__Internal")]
	private static extern void _createOurAdsInterstitial(string imgUrl, bool isGetLocal);
	
	[DllImport("__Internal")]
	private static extern void _addCode(string key_name);
	
	
	[DllImport("__Internal")]
	private static extern void _showFeedBack();

    [DllImport("__Internal")]
    private static extern void _openAppSetting();

    [DllImport("__Internal")]
    private static extern void _requestReview();

    [DllImport("__Internal")]
    private static extern bool _isIphoneX();

	[DllImport("__Internal")]
	private static extern string _webviewAdjustedRect(float top, float left, float bottom, float right,
								  float point_x, float point_y, float width, float height);

	[DllImport("__Internal")]
	private static extern bool _isViaWiFi();

	// Check Content is unlock by product identifiers
	public static bool IOSCheckContentUnLock( string productIdentifier )
    {
        if (Application.platform == RuntimePlatform.IPhonePlayer)
			return _iappsteamIOSCheckContentUnLock(productIdentifier);
		
		return false;
    }
	
	public static bool ConnectedToNetwork()
    {
        if( Application.platform == RuntimePlatform.IPhonePlayer )
			return _connectedToNetwork();
		
		return false;
    }

	public static string CheckUserOSLanguage()
	{
		if (Application.platform == RuntimePlatform.IPhonePlayer)
		{
			return _checkUserOSLanguage();
		}
		else
		{
			return "en";
		}
			
    }
	
	public static void MobClickBeginEvent(string event_name)
    {
        if( Application.platform == RuntimePlatform.IPhonePlayer )
			_mobClick_beginEvent(event_name);
    }
	
	public static void MobClickEndEvent(string event_name)
    {
        if( Application.platform == RuntimePlatform.IPhonePlayer )
			_mobClick_endEvent(event_name);
    }
	
	public static void MobClickEvent(string event_name)
    {
        if( Application.platform == RuntimePlatform.IPhonePlayer )
			_mobClick_event(event_name);
    }
    
	public static void Umeng_updateOnlineConfig()
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_umeng_updateOnlineConfig();
	}
	
	/*
	public static bool is_ipad()
    {
        if( Application.platform == RuntimePlatform.IPhonePlayer )
			return _is_ipad();
		
		return false;
    }
	
	public static void ShowFeedBack()
    {
        if( Application.platform == RuntimePlatform.IPhonePlayer )
			_showFeedBack();
    }
	*/
	public static float IOSSystemVersion()
    {
		float os_version = 4.3f;
        if( Application.platform == RuntimePlatform.IPhonePlayer )
		{
			os_version = _systemVersion();
			//print ("systemVersion:" + os_version.ToString());
		}
		
		return os_version;
    }
	/*
	public static void SetUnityPause(bool flag)
    {
        if( Application.platform == RuntimePlatform.IPhonePlayer )
			_setUnityPause(flag);
    }
*/

	public static void ShowFeedBack()
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_showFeedBack();
	}

	public static string GetConfigParams(string key_name)
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			return _getConfigParams(key_name);
		
		return "";
	}
	
	public static void LoadOurAdsData(float x, float y, float w, float h)
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_loadOurAdsData(x, y, w, h);
	}
	
	public static void RemoveOurAds()
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_removeOurAds();
	}
	
	public static void GetLanguageUserFlag(string flag)
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_getLanguageUserFlag(flag);
	}
	
	public static void RemoveOurAdsInterstitial()
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_removeOurAdsInterstitial();
	}
	
	public static void CreateOurAdsInterstitial(string imgUrl, bool isGetLocal)
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_createOurAdsInterstitial(imgUrl, isGetLocal);
	}
	
	public static void AddCode(string key_name)
	{
		if( Application.platform == RuntimePlatform.IPhonePlayer )
			_addCode(key_name);
	}
	
    public static void OpenAppSetting()
    {
        if (Application.platform == RuntimePlatform.IPhonePlayer)
            _openAppSetting();
    }

    public static void RequestAppstoreReview()
    {
        if (Application.platform == RuntimePlatform.IPhonePlayer)
            _requestReview();
    }

    public static bool IsIphoneX()
    {
        if (Application.platform == RuntimePlatform.IPhonePlayer)
            return _isIphoneX();

        return false;
    }

	public static string Webview_AdjustedRects(float top, float left, float bottom, float right,
								  float point_x, float point_y, float width, float height)
	{
		if (Application.platform == RuntimePlatform.IPhonePlayer)
			return _webviewAdjustedRect(top, left, bottom, right,
								  point_x, point_y, width, height);
		return "{{10, 10}, {110, 60}}";
	}

	public static bool IsViaWiFi()
	{
		if (Application.platform == RuntimePlatform.IPhonePlayer)
			return _isViaWiFi();

		return false;
	}

    [DllImport("__Internal")]
    private static extern bool _isIpad();

    [DllImport("__Internal")]
    private static extern bool _checkIpadHasHomeIndicator();


    public static bool isIpad()
    {
        if (Application.platform == RuntimePlatform.IPhonePlayer)
            return _isIpad();

        return false;
    }

    public static bool CheckIpadHasHomeIndicator()
    {
        if (Application.platform == RuntimePlatform.IPhonePlayer)
            return _checkIpadHasHomeIndicator();

        return false;
    }
}
#endif