using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using GoogleMobileAds.Api;
using UnityEngine.UI;
using LitJson;


public class AdsBannerControl : MonoBehaviour
{

    public enum NormalBannerPosition
    {
        UpperLeft,
        UpperCenter,
        UpperRight,
        MiddleCenter,
        LowerLeft,
        LowerCenter,
        LowerRight
    };

    public NormalBannerPosition normalBannerPosit = NormalBannerPosition.UpperLeft;
    public bool canShowInterstitialAds = false, canShowBanner = true, canShowNativeAds = false;

    private static bool IsInterstisialsAdReady = false;
    private int googleBannerId = 0;

    private AdPosition googleAdsPosi = AdPosition.Top;
    private BaiduMobAdPosition baiduMobAdsPosi = BaiduMobAdPosition.TopLeft;
    private GDTMobAdPosition gdtMobAdsPosi = GDTMobAdPosition.TopLeft;
    private GDTMobNativeAdPosition gdtMobNativeAdPosition = GDTMobNativeAdPosition.Bottom;
    //private AdPosition adMobNativeAdPosition = AdPosition.Bottom;
    //private BannerView admobBannerView;
    //private InterstitialAd admobInterstitial;

    private BannerView admobBannerView;
    private InterstitialAd admobInterstitial;
    private bool IsAdmobInterstisialsAdReady = false;

    private string
    BaiduMob_ID = "d45cb871", BaiduMob_AdUnitTag1 = "6001926", BaiduMob_AdUnitTag2 = "6001927";

    //old id
    private string AdMob_Banner1 = "ca-app-pub-4928974814720372/9305743298",
        AdMob_Banner2 = "ca-app-pub-4928974814720372/7235728127";

    private static string GDT_Banner1 = "4021210393226130", 
    //GDT_Banner2 = "5051910393228141",
    GDT_Banner2 = "4055180151492722",
    GDT_NativeAdsUnitId = "", GDT_Appkey = "1107968075";

    private const string
            Banner_Admob = "gads.banner", Banner_BaiduMob = "", Banner_GDT = "gdt.banner",
            Interstisial_Admob = "gads.interads", Interstisial_BaiduMob = "",
            Interstisial_GDTMob = "gdt.interads", Interstisial_UnionMob = "interstisial_unionMob",
            NativeAds_Admob = "nativeAds_admob", NativeAds_GDTMob = "nativeAds_gdtMob",
            BaiduMob_cIDPermissionFlag = "baiduMob_CIDPer";

    private static bool canShowAdmob = false;
    private static bool canShowGDT = false, canShowAdmobBanner = false, canShowGDTBanner = false;
    private int normalAds_Admob = 50, normalAds_Baidu = 10, normalAds_GDT = 10;
    private int interstisialsAds_Admob = 50, interstisialsAds_Baidu = 10, interstisialsAds_GDT = 0, interstisialsAds_Union = 50;
    private int nativeAds_Admob = 50, nativeAds_GDT = 50;
    private string flag_AdmobDateClickCount = "AdmobDateClickCount", flag_AdmobBannerDateClickCount = "AdmobBannerDateClickCount";
    private string flag_BaiduDateClickCount = "BaiduDateClickCount", flag_BaiduBannerDateClickCount = "BaiduBannerDateClickCount";
    private string flag_GDTDateClickCount = "GDTDateClickCount", flag_GDTBannerDateClickCount = "GDTBannerDateClickCount";

    //private NativeExpressAdView nativeExpressAdView_Admob;
    //private bool Admob_NativeExpress_isReady = false;
    private bool GDTMob_NativeExpress_isReady = false;

    
    private string[] admob_testDevices = new string[] {
        "2de320175027d4afbea09fcc80bfa6a0", "9875f751c17a6ce92d811655b8e933e0",
        "9db338f7c73a3c52088f063ed6adb089", "e8fc3d6c9b7caed87dcf91b62f770d74",
        "ec81bad5706562613aaf31e42d8d939e", "07fb15993a5a1bd709ced38e2c9403d7", "9db338f7c73a3c52088f063ed6adb089",
        "e8fc3d6c9b7caed87dcf91b62f770d74", "21230ef208850b3ff059ad63ff0b180e", "5a068597458f4788436577acc059a3f8",
        "6a71ff52076ffdd72d943d6da3e970e3", "f637c82195acf88a4eb30d87102b6cac" };
   

    //private string[] admob_testDevices = new string[] { "" };

    private List<string> normalAds_list = new List<string>();
    private string[] normalAds_array;

    private List<string> intersAds_list = new List<string>();
    private string[] intersAds_array;

    private int last_normalAds_Admob = 99, last_normalAds_Baidu = 99, last_normalAds_GDT = 99;
    private int last_interstisialsAds_Admob = 99, last_interstisialsAds_Baidu = 99, last_interstisialsAds_GDT = 99;

    private GameObject bannerBlackBg;

    // Use this for initialization

    void Awake()
    {
        DontDestroyOnLoad(gameObject);

        bannerBlackBg = transform.Find("Canvas").Find("BannerBlackBg").gameObject;

        RectTransform rt_bannerBlackBg = bannerBlackBg.GetComponent<RectTransform>();

        rt_bannerBlackBg.sizeDelta = new Vector2(Screen.width, 0);
    }

    void Update()
    {
        //if (GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.Admob)
        //{
            
            if (admobInterstitial != null && admobInterstitial.CanShowAd())
            {
                if (IsAdmobInterstisialsAdReady == false)
                    HandleInterstitialLoaded(); 
            }
            
        //}
        
    }

    void Start()
    {
        GlobalVariable.adsControl_count++;

        if (GlobalVariable.adsControl_count > 1)
        {
            Debug.LogWarning("!!! Ads Banner Control has existed, Destroy it !!!");
            Destroy(gameObject);
        }

        InitData();
    }

    void InitData()
    {
        Debug.Log("banner object:" + gameObject.name);

    }

    public void DestroyBanners()
    {
        BaiduMobBinding.destroyBanner();
        GDTMobBinding.destroyBanner();

        if (admobBannerView != null) admobBannerView.Destroy();
        if (admobInterstitial != null) admobInterstitial.Destroy();

        bannerBlackBg.SetActive(false);
    }


    void SetBannerBlackBgHeightAndPosition(float banner_height)
    {
        float need_height = banner_height * 1.2f;

        if(IAppsTeamIOSUntil.IsIphoneX()) need_height = banner_height * 1.5f;

        if(IAppsTeamIOSUntil.isIpad() && IAppsTeamIOSUntil.CheckIpadHasHomeIndicator())
        {
            need_height = banner_height * 1.6f;
        }

        bannerBlackBg.GetComponent<Image>().SetNativeSize();

        RectTransform rt = bannerBlackBg.GetComponent<RectTransform>();

        rt.sizeDelta = new Vector2(Screen.width, need_height);

        if(normalBannerPosit == NormalBannerPosition.LowerCenter
            || normalBannerPosit == NormalBannerPosition.LowerLeft
            || normalBannerPosit == NormalBannerPosition.LowerRight)
        {
            rt.position = new Vector2(rt.position.x, 0);
        }
        else
        {
            rt.position = new Vector2(rt.position.x, Screen.height - need_height);
        }

        bannerBlackBg.SetActive(true);
    }

    public int GetNoAdsShowCount()
    {
        int count = 0;

        count = GetIntFromConfig("interads", "interval");

        Debug.LogFormat("GetNoAdsShowCount: {0}", count);

        return count;
    }

    public void LoadData()
    {
        GlobalVariable.hasClickCount_Admob_InterAds = false;
        GlobalVariable.hasClickCount_Admob_BannerAds = false;

        GlobalVariable.hasClickCount_Baidu_InterAds = false;
        GlobalVariable.hasClickCount_Baidu_BannerAds = false;

        GlobalVariable.hasClickCount_GDT_InterAds = false;

        DestroyBanners();

        canShowNativeAds = GetCanNativeAds();
        canShowNativeAds = false;

        gameObject.name = "BannerControl";
        canShowAdmob = false;
        canShowGDT = false;
        normalAds_Admob = GetAdsShowRate(Banner_Admob);
        normalAds_Baidu = 0;
        normalAds_GDT = GetAdsShowRate(Banner_GDT, 0);

        //normalAds_Admob = 5;
        //normalAds_Baidu = 1;
        //normalAds_GDT = 1;

        interstisialsAds_Admob = GetAdsShowRate(Interstisial_Admob);
        interstisialsAds_Baidu = 0;
        interstisialsAds_GDT = GetAdsShowRate(Interstisial_GDTMob, 0);
        //interstisialsAds_GDT = 0;
        //interstisialsAds_Union = GetAdsShowRate(Interstisial_UnionMob);

        //nativeAds_Admob = GetAdsShowRate(NativeAds_Admob);
        //nativeAds_GDT = GetAdsShowRate(NativeAds_GDTMob);


        //interstisialsAds_Admob = 5;
        //interstisialsAds_Baidu = 0;
        //interstisialsAds_GDT = 1;
        //interstisialsAds_Union = 2;


        Debug.Log("=====================:");
        Debug.Log("====normalAds_Admob====:" + normalAds_Admob);
        Debug.Log("====normalAds_Baidu====:" + normalAds_Baidu);
        Debug.Log("====normalAds_GDT====:" + normalAds_GDT);

        Debug.Log("====interstisialsAds_Admob====:" + interstisialsAds_Admob);
        Debug.Log("====interstisialsAds_Baidu====:" + interstisialsAds_Baidu);
        Debug.Log("====interstisialsAds_GDT====:" + interstisialsAds_GDT);
        //Debug.Log("====interstisialsAds_Union====:" + interstisialsAds_Union);
        Debug.Log("=====================:");

        Debug.Log("GetAdmobOpenInterAdsCount :" + GetAdmobOpenInterAdsCount().ToString());

        /*
        if (AdsSwticher() == "u1")
        {
            Debug.Log("now: u1");
            //new us
            AdMob_Banner1 = "ca-app-pub-1540672515686475/9844818124";
            AdMob_Banner2 = "ca-app-pub-1540672515686475/1906618352";
        }
        else if(AdsSwticher() == "u2")
        {
            Debug.Log("now: u2");

            //new yuan
            AdMob_Banner1 = "ca-app-pub-4928974814720372/9669067036";
            AdMob_Banner2 = "ca-app-pub-4928974814720372/7478017960";
        }

        //test id
        /*
        AdMob_Banner1 = "ca-app-pub-3940256099942544/2934735716";
        AdMob_Banner2 = "ca-app-pub-3940256099942544/4411468910";

        BaiduMob_ID = "ccb60059";
        BaiduMob_AdUnitTag1 = "3722589";
        BaiduMob_AdUnitTag2 = "2058554";
        */


        //setting normalAds
        if (normalAds_Admob != last_normalAds_Admob || normalAds_Baidu != last_normalAds_Baidu ||
            normalAds_GDT != last_normalAds_GDT)
        {
            Debug.Log("need change normalAds_list");
            normalAds_list.Clear();

            int emptyMob_Count = GetNoAdsShowCount();

            for (int idx = 0; idx < normalAds_Admob; idx++)
            {
                normalAds_list.Add(GlobalVariable.NormalAdsMobType.Admob.ToString());
            }

            for (int idx = 0; idx < normalAds_Baidu; idx++)
            {
                normalAds_list.Add(GlobalVariable.NormalAdsMobType.BaiduMob.ToString());
            }

            for (int idx = 0; idx < normalAds_GDT; idx++)
            {
                normalAds_list.Add(GlobalVariable.NormalAdsMobType.GdtMob.ToString());
            }

            string s1 = "normalAds_list : ";
            foreach (string s in normalAds_list)
            {
                s1 += s + " ";
            }
            Debug.Log(s1);
            normalAds_array = normalAds_list.ToArray();

            last_normalAds_Admob = normalAds_Admob;
            last_normalAds_Baidu = normalAds_Baidu;
            last_normalAds_GDT = normalAds_GDT;

            GlobalVariable.showNoramlAdsCount = -1;
        }


        /*
        string interstisials_configs = PlayerPrefs.GetString(GlobalVariable.AdsInter_Id, "gd");
        if (interstisials_configs == "ad")//admob
        {
            interstisialsAds_Admob = 3;
            interstisialsAds_Baidu = 0;
            interstisialsAds_GDT = 0;
        }
        else
        {
            interstisialsAds_Admob = 0;
            interstisialsAds_Baidu = 0;
            interstisialsAds_GDT = 3;
        }
        

        string flag_InterAdsNeedChange = InterAdsNeedChange();
        if (flag_InterAdsNeedChange == "ad")//admob
        {
            interstisialsAds_Admob = 3;
            interstisialsAds_Baidu = 0;
            interstisialsAds_GDT = 0;
            Debug.Log("set all inter_ads is admob");
        }
        else if (flag_InterAdsNeedChange == "gd")//gdt
        {
            interstisialsAds_Admob = 0;
            interstisialsAds_Baidu = 0;
            interstisialsAds_GDT = 3;
            Debug.Log("set all inter_ads is gdt");
        };
        */

        //setting interstisialsAds
        if (interstisialsAds_Admob != last_interstisialsAds_Admob || interstisialsAds_Baidu != last_interstisialsAds_Baidu ||
            interstisialsAds_GDT != last_interstisialsAds_GDT)
        {
            Debug.Log("need change intersAds_list");
            intersAds_list.Clear();

            int emptyMob_Count = GetNoAdsShowCount();

            for (int idx = 0; idx < interstisialsAds_Admob; idx++)
            {
                intersAds_list.Add(GlobalVariable.InterstitialAdsType.Admob.ToString());

                if (emptyMob_Count > 0)
                {
                    for (int idx1 = 0; idx1 < emptyMob_Count; idx1++)
                    {
                        intersAds_list.Add(GlobalVariable.InterstitialAdsType.EmptyMob.ToString());
                    }
                }
            }

            for (int idx = 0; idx < interstisialsAds_Baidu; idx++)
            {
                intersAds_list.Add(GlobalVariable.InterstitialAdsType.BaiduMob.ToString());

                if (emptyMob_Count > 0)
                {
                    for (int idx1 = 0; idx1 < emptyMob_Count; idx1++)
                    {
                        intersAds_list.Add(GlobalVariable.InterstitialAdsType.EmptyMob.ToString());
                    }
                }
            }

            for (int idx = 0; idx < interstisialsAds_GDT; idx++)
            {
                intersAds_list.Add(GlobalVariable.InterstitialAdsType.GdtMob.ToString());

                if (emptyMob_Count > 0)
                {
                    for (int idx1 = 0; idx1 < emptyMob_Count; idx1++)
                    {
                        intersAds_list.Add(GlobalVariable.InterstitialAdsType.EmptyMob.ToString());
                    }
                }
            }

            string s1 = "intersAds_list : ";
            foreach (string s in intersAds_list)
            {
                s1 += s + " ";
            }
            Debug.Log(s1);
            intersAds_array = intersAds_list.ToArray();

            last_interstisialsAds_Admob = interstisialsAds_Admob;
            last_interstisialsAds_Baidu = interstisialsAds_Baidu;
            last_interstisialsAds_GDT = interstisialsAds_GDT;

            GlobalVariable.showInterstitialAdsCount = -1;
        }
      
        
        /*
        if (CanInterstitialAd() == false)
        {
            canShowInterstitialAds = false;
            Debug.Log("canShowInterstitialAds is false");

            canShowNativeAds = false;
            Debug.Log("canShowNativeAds is false");
        }
        */

        if (IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1))
        {
            canShowInterstitialAds = false;
        }


        AdvsSettingInit();

        ShowNormalAd();
    }


        #region Admob Banner callback handlers
        public void  AdmobBanner_OnLeftApplicationAction()
        {
                print ("AdmobBanner_OnLeftApplicationAction event received");
                if(!GlobalVariable.hasClickCount_Admob_BannerAds)
                {
                    GlobalVariable.hasClickCount_Admob_BannerAds = true;
                    CountAdmobOpenBannerAds();
                }
        }

        void CountAdmobOpenBannerAds()
        {
            string nowDate = DateTime.Now.ToString("yyyy-MM-dd");

            string strNowDateClickCount = PlayerPrefs.GetString(flag_AdmobBannerDateClickCount);

            if (strNowDateClickCount == "" || strNowDateClickCount == null)
            {
                strNowDateClickCount = nowDate + ":1";
            }
            else
            {
                int now_click = 0;
                string[] aStr = strNowDateClickCount.Split(':');
                if (aStr[0] == nowDate)
                {
                    now_click = int.Parse(aStr[1]) + 1;
                    strNowDateClickCount = nowDate + ":" + now_click;
                }
                else
                {
                    strNowDateClickCount = nowDate + ":1";
                }
            }

            PlayerPrefs.SetString(flag_AdmobBannerDateClickCount, strNowDateClickCount);
            Debug.Log(flag_AdmobBannerDateClickCount + ": " + PlayerPrefs.GetString(flag_AdmobBannerDateClickCount));
        }

        int GetAdmobOpenBannerAdsCount()
        {
            int click_count = 0;

            string strNowDateClickCount = PlayerPrefs.GetString(flag_AdmobBannerDateClickCount);
            Debug.Log("Admob Banner strNowDateClickCount :" + strNowDateClickCount.ToString());
            if (strNowDateClickCount == "" || strNowDateClickCount == null)
            {
                click_count = 0;
            }
            else
            {
                string nowDate = DateTime.Now.ToString("yyyy-MM-dd");
                string[] aStr = strNowDateClickCount.Split(':');
                if (aStr[0] == nowDate)
                {
                    click_count = int.Parse(aStr[1]);
                }
                else
                {
                    click_count = 0;
                }
            }

            return click_count;
        }

        #endregion
	
		#region Admob Interstitial callback handlers

        public void HandleInterstitialLoaded ()
		{
				IsAdmobInterstisialsAdReady = true;
				print ("Admob HandleInterstitialLoaded event received.");
		}

        public void HandleInterstitialFailedToLoad ()
		{
				print ("Admob HandleInterstitialFailedToLoad event - Create GDT");
				if (canShowGDT)
						return;
				canShowGDT = true;
				CreateInterstitialAd_GDTmob ();
		}

        public void HandleInterstitialOpened ()
		{
				IsAdmobInterstisialsAdReady = false;
				print ("Admob HandleInterstitialOpened event received");
		}

	
        public void HandleInterstitialClosed ()
		{
				print ("Admob HandleInterstitialClosed event received");
		}

        public void HandleInterstitialLeftApplication ()
		{
				print ("Admob HandleInterstitialLeftApplication event received");
                if(!GlobalVariable.hasClickCount_Admob_InterAds)
                {
                    GlobalVariable.hasClickCount_Admob_InterAds = true;
                    CountAdmobOpenInterAds();
                }
                
                
		}

        public void Admob_OnBannerAdFailedToLoad(GoogleMobileAdBanner banner)
        {
            print("Admob OnBannerAdFailedToLoad - show gdt banner");
            /*
            if (canShowGDTBanner)
                return;
            canShowGDTBanner = true;
            CreateNormalBanner_GDTmob();
            admobBannerView.OnFailedLoadingAction -= Admob_OnBannerAdFailedToLoad;
            */
        }

        void CountAdmobOpenInterAds()
        {
            string nowDate = DateTime.Now.ToString("yyyy-MM-dd"); 

            string strNowDateClickCount = PlayerPrefs.GetString(flag_AdmobDateClickCount);

            if(strNowDateClickCount == "" || strNowDateClickCount == null)
            {
                strNowDateClickCount = nowDate + ":1";
            }
            else
            {
                int now_click = 0;
                string[] aStr = strNowDateClickCount.Split(':');
                if(aStr[0] == nowDate)
                {
                    now_click = int.Parse(aStr[1]) + 1;
                    strNowDateClickCount = nowDate + ":" +now_click;
                }
                else
                {
                    strNowDateClickCount = nowDate + ":1";
                }
            }

            PlayerPrefs.SetString(flag_AdmobDateClickCount, strNowDateClickCount);
            Debug.Log(flag_AdmobDateClickCount + ": " + PlayerPrefs.GetString(flag_AdmobDateClickCount));
        }

        int GetAdmobOpenInterAdsCount()
        {
            int click_count = 0;

            string strNowDateClickCount = PlayerPrefs.GetString(flag_AdmobDateClickCount);
            Debug.Log("Admob InterAds strNowDateClickCount :" + strNowDateClickCount.ToString());

            if (strNowDateClickCount == "" || strNowDateClickCount == null)
            {
                click_count = 0;
            }
            else
            {
                string nowDate = DateTime.Now.ToString("yyyy-MM-dd"); 
                string[] aStr = strNowDateClickCount.Split(':');
                if (aStr[0] == nowDate)
                {
                    click_count = int.Parse(aStr[1]);
                }
                else
                {
                    click_count = 0;
                }
            }

            return click_count;
        }

    #endregion

    #region Baidu Banner callback handlers
    public void BaiduBanner_OnLeftApplicationAction()
    {
        print("BaiduBanner_OnLeftApplicationAction event received");
        if (!GlobalVariable.hasClickCount_Baidu_BannerAds)
        {
            GlobalVariable.hasClickCount_Baidu_BannerAds = true;
            CountBaiduOpenBannerAds();
        }
    }

    void CountBaiduOpenBannerAds()
    {
        string nowDate = DateTime.Now.ToString("yyyy-MM-dd");

        string strNowDateClickCount = PlayerPrefs.GetString(flag_BaiduBannerDateClickCount);

        if (strNowDateClickCount == "" || strNowDateClickCount == null)
        {
            strNowDateClickCount = nowDate + ":1";
        }
        else
        {
            int now_click = 0;
            string[] aStr = strNowDateClickCount.Split(':');
            if (aStr[0] == nowDate)
            {
                now_click = int.Parse(aStr[1]) + 1;
                strNowDateClickCount = nowDate + ":" + now_click;
            }
            else
            {
                strNowDateClickCount = nowDate + ":1";
            }
        }

        PlayerPrefs.SetString(flag_BaiduBannerDateClickCount, strNowDateClickCount);
        Debug.Log(flag_BaiduBannerDateClickCount + ": " + PlayerPrefs.GetString(flag_BaiduBannerDateClickCount));
    }

    int GetBaiduOpenBannerAdsCount()
    {
        int click_count = 0;

        string strNowDateClickCount = PlayerPrefs.GetString(flag_BaiduBannerDateClickCount);
        Debug.Log("Baidu Banner strNowDateClickCount :" + strNowDateClickCount.ToString());
        if (strNowDateClickCount == "" || strNowDateClickCount == null)
        {
            click_count = 0;
        }
        else
        {
            string nowDate = DateTime.Now.ToString("yyyy-MM-dd");
            string[] aStr = strNowDateClickCount.Split(':');
            if (aStr[0] == nowDate)
            {
                click_count = int.Parse(aStr[1]);
            }
            else
            {
                click_count = 0;
            }
        }

        return click_count;
    }

    #endregion
    #region baidu Interstitial callback handlers

    void baiduInterstitialFailToLoadAd (string empty)
		{
        /*
				Debug.Log ("baiduInterstitialFailToLoadAd - Create GDT");
				if (canShowGDT)
						return;
				canShowGDT = true;
				CreateInterstitialAd_GDTmob ();
                */
		}

		void baiduInterstitialFailPresentScreen (string empty)
		{
				Debug.Log ("baiduInterstitialFailPresentScreen - Create GDT");
				/*
		
		if(canShowGDT) return;
		canShowGDT = true;
		CreateInterstitialAd_GDTmob();
		*/
		}

        void Baidu_HandleInterstitialLeftApplication()
        {
            print("Baidu HandleInterstitialLeftApplication event received");
            if (!GlobalVariable.hasClickCount_Baidu_InterAds)
            {
                GlobalVariable.hasClickCount_Baidu_InterAds = true;
                CountBaiduOpenInterAds();
            }
        }

        void CountBaiduOpenInterAds()
        {
            string nowDate = DateTime.Now.ToString("yyyy-MM-dd");

            string strNowDateClickCount = PlayerPrefs.GetString(flag_BaiduDateClickCount);

            if (strNowDateClickCount == "" || strNowDateClickCount == null)
            {
                strNowDateClickCount = nowDate + ":1";
            }
            else
            {
                int now_click = 0;
                string[] aStr = strNowDateClickCount.Split(':');
                if (aStr[0] == nowDate)
                {
                    now_click = int.Parse(aStr[1]) + 1;
                    strNowDateClickCount = nowDate + ":" + now_click;
                }
                else
                {
                    strNowDateClickCount = nowDate + ":1";
                }
            }

            PlayerPrefs.SetString(flag_BaiduDateClickCount, strNowDateClickCount);
            Debug.Log(flag_BaiduDateClickCount + ": " + PlayerPrefs.GetString(flag_BaiduDateClickCount));
        }

    int GetBaiduOpenInterAdsCount()
    {
        int click_count = 0;

        string strNowDateClickCount = PlayerPrefs.GetString(flag_BaiduDateClickCount);

        Debug.Log("Baidu InterAds strNowDateClickCount :" + strNowDateClickCount.ToString());
        if (strNowDateClickCount == "" || strNowDateClickCount == null)
        {
            click_count = 0;
        }
        else
        {
            string nowDate = DateTime.Now.ToString("yyyy-MM-dd");
            string[] aStr = strNowDateClickCount.Split(':');
            if (aStr[0] == nowDate)
            {
                click_count = int.Parse(aStr[1]);
            }
            else
            {
                click_count = 0;
            }
        }

        return click_count;
    }

		#endregion

		#region GDT Interstitial callback handlers

		public void GdtInterstitialFailToLoadAd()
		{
				Debug.Log ("GdtInterstitialFailToLoadAd - create admob");
				if (canShowAdmob)
						return;
				canShowAdmob = true;
				CreateInterstitialAd_Admob ();
		}

        

    public void GDT_UnifiedInterstitialClicked()
    {
        print("GDT unifiedInterstitialClicked event received");
        if (!GlobalVariable.hasClickCount_GDT_InterAds)
        {
            GlobalVariable.hasClickCount_GDT_InterAds = true;
            CountGDTOpenInterAds();
        }

    }



    void CountGDTOpenInterAds()
    {
        string nowDate = DateTime.Now.ToString("yyyy-MM-dd");

        string strNowDateClickCount = PlayerPrefs.GetString(flag_GDTDateClickCount);

        if (strNowDateClickCount == "" || strNowDateClickCount == null)
        {
            strNowDateClickCount = nowDate + ":1";
        }
        else
        {
            int now_click = 0;
            string[] aStr = strNowDateClickCount.Split(':');
            if (aStr[0] == nowDate)
            {
                now_click = int.Parse(aStr[1]) + 1;
                strNowDateClickCount = nowDate + ":" + now_click;
            }
            else
            {
                strNowDateClickCount = nowDate + ":1";
            }
        }

        PlayerPrefs.SetString(flag_GDTDateClickCount, strNowDateClickCount);
        Debug.Log(flag_GDTDateClickCount + ": " + PlayerPrefs.GetString(flag_GDTDateClickCount));
    }

    int GetGDTOpenInterAdsCount()
    {
        int click_count = 0;

        string strNowDateClickCount = PlayerPrefs.GetString(flag_GDTDateClickCount);
        Debug.Log("GDT InterAds strNowDateClickCount :" + strNowDateClickCount.ToString());

        if (strNowDateClickCount == "" || strNowDateClickCount == null)
        {
            click_count = 0;
        }
        else
        {
            string nowDate = DateTime.Now.ToString("yyyy-MM-dd");
            string[] aStr = strNowDateClickCount.Split(':');
            if (aStr[0] == nowDate)
            {
                click_count = int.Parse(aStr[1]);
            }
            else
            {
                click_count = 0;
            }
        }

        return click_count;
    }

    #endregion



    void AdvsSettingInit ()
		{
				if (IAppsTeamIOSUntil.IOSCheckContentUnLock (GlobalVariable.IAP_ID1))
						return; 
	    
                if (normalBannerPosit == NormalBannerPosition.LowerCenter) {
                        googleAdsPosi = AdPosition.Bottom;
                        baiduMobAdsPosi = BaiduMobAdPosition.BottomCenter;
                        gdtMobAdsPosi = GDTMobAdPosition.BottomCenter;
                } else if (normalBannerPosit == NormalBannerPosition.LowerLeft) {
                        googleAdsPosi = AdPosition.BottomLeft;
                        baiduMobAdsPosi = BaiduMobAdPosition.BottomLeft;
                        gdtMobAdsPosi = GDTMobAdPosition.BottomLeft;
                } else if (normalBannerPosit == NormalBannerPosition.LowerRight) {
                        googleAdsPosi = AdPosition.BottomRight;
                        baiduMobAdsPosi = BaiduMobAdPosition.BottomRight;
                        gdtMobAdsPosi = GDTMobAdPosition.BottomRight;
                } else if (normalBannerPosit == NormalBannerPosition.MiddleCenter) {
                        googleAdsPosi = AdPosition.Center;
                        baiduMobAdsPosi = BaiduMobAdPosition.Centered;
                        gdtMobAdsPosi = GDTMobAdPosition.Centered;
                } else if (normalBannerPosit == NormalBannerPosition.UpperLeft) {
                        googleAdsPosi = AdPosition.TopLeft;
                        baiduMobAdsPosi = BaiduMobAdPosition.TopLeft;
                        gdtMobAdsPosi = GDTMobAdPosition.TopLeft;
                } else if (normalBannerPosit == NormalBannerPosition.UpperRight) {
                        googleAdsPosi = AdPosition.TopRight;
                        baiduMobAdsPosi = BaiduMobAdPosition.TopRight;
                        gdtMobAdsPosi = GDTMobAdPosition.TopRight;
                } else if (normalBannerPosit == NormalBannerPosition.UpperCenter) {
                        googleAdsPosi = AdPosition.Top;
                        baiduMobAdsPosi = BaiduMobAdPosition.TopCenter;
                        gdtMobAdsPosi = GDTMobAdPosition.TopCenter;
                } else {
                        googleAdsPosi = AdPosition.Top;
                        baiduMobAdsPosi = BaiduMobAdPosition.TopCenter;
                        gdtMobAdsPosi = GDTMobAdPosition.TopCenter;
                }
    


                SwitchAds();
				
		}

	
		void CreateNormalBanner_Admob ()
		{
				
		        // Create a 320x50 banner at the top of the screen.
			if (!canShowBanner)
						return;

            int click_limit = BannerAds_AdmobClickCount();

            Debug.Log("today admob Banner click_limit: " + click_limit.ToString());

            Debug.Log("today GetAdmobOpenBannerAdsCount(): " + GetAdmobOpenBannerAdsCount().ToString());

            if (GetAdmobOpenBannerAdsCount() < click_limit)
            {
                bannerBlackBg.SetActive(true);

                admobBannerView = new BannerView(AdMob_Banner1, GoogleMobileAds.Api.AdSize.Banner, googleAdsPosi);

            float admobBannerView_Height = admobBannerView.GetHeightInPixels();
            Debug.Log(string.Format("Admob_BannerView_Height:{0}", admobBannerView_Height.ToString()));

            SetBannerBlackBgHeightAndPosition(admobBannerView_Height);


            // Create an empty ad request.
                AdRequest request = new AdRequest();

                // Load the banner with the request.
                admobBannerView.LoadAd(request);

          

                //admobBannerView.OnAdLeavingApplication += AdmobBanner_OnLeftApplicationAction;
            admobBannerView.OnAdClicked += () =>
                {
                    Debug.Log("Banner OnAdClicked.");
                    AdmobBanner_OnLeftApplicationAction();
                };

            
        }
            else
            {
                if (GlobalVariable.GetDeviceOSLanguage() == "sc")
                {
                    string replace_banner = BannerAdsReplaceAdmob();
                    if(replace_banner == "baidu")
                    {
                        GlobalVariable.normalAdsMobType = GlobalVariable.NormalAdsMobType.BaiduMob;
                        CreateNormalBanner_Baidumob();
                        Debug.Log("today admob Banner have click over " + click_limit.ToString() + " time");
                        Debug.Log(" create baidu banner ");
                    }
                    else
                    {
                        GlobalVariable.normalAdsMobType = GlobalVariable.NormalAdsMobType.GdtMob;
                        CreateNormalBanner_GDTmob();
                        Debug.Log("today admob Banner have click over " + click_limit.ToString() + " time");
                        Debug.Log(" create gdt banner ");
                    }
                }
                else
                {
                    Debug.Log("today admob Banner have click over " + click_limit.ToString() + " time");
                    canShowBanner = false;
                }
            }
				
		}

		void CreateNormalBanner_Baidumob ()
		{
			if (!canShowBanner)
						return;
            
            int click_limit = BannerAds_BaiduClickCount();

            Debug.Log("today Baidu Banner click_limit: " + click_limit.ToString());

            Debug.Log("today GetBaiduOpenBannerAdsCount(): " + GetBaiduOpenBannerAdsCount().ToString());

            if (GetBaiduOpenBannerAdsCount() < click_limit)
            {
                BaiduMobBinding.createBanner(BaiduMobBannerType.BaiduMobBannerType_320x48, baiduMobAdsPosi, BaiduMob_AdUnitTag1);
            }
			else
            {
                if (GlobalVariable.GetDeviceOSLanguage() == "sc")
                {
                    GlobalVariable.normalAdsMobType = GlobalVariable.NormalAdsMobType.GdtMob;
                    CreateNormalBanner_GDTmob();
                    Debug.Log("today Baidu Banner have click over " + click_limit.ToString() + " time");
                    Debug.Log(" create gdt banner ");
                }
                else
                {
                    Debug.Log("today Baidu Banner have click over " + click_limit.ToString() + " time");
                    canShowBanner = false;
                }
        }
            
		}


        void CreateNormalBanner_GDTmob ()
		{
                if (!canShowBanner)
						return;

                int click_limit = BannerAds_GDTClickCount();

                Debug.Log("today GDT Banner click_limit: " + click_limit.ToString());

                Debug.Log("today GetGDTOpenBannerAdsCount(): " + GetGDTOpenBannerAdsCount().ToString());

            if (GetGDTOpenBannerAdsCount() < click_limit)
            {

                Debug.Log("CreateNormalBanner_GDTmob");

                GDTMobBinding.createBanner(GDTMobBannerType.GDTMobBannerType_320x50, gdtMobAdsPosi);
            }
            else
            {
                Debug.Log("today GDT Banner have click over " + click_limit.ToString() + " time");
                canShowBanner = false;
            }
            
        }

    void CountGDTOpenBannerAds()
    {
        string nowDate = DateTime.Now.ToString("yyyy-MM-dd");

        string strNowDateClickCount = PlayerPrefs.GetString(flag_GDTBannerDateClickCount);

        if (strNowDateClickCount == "" || strNowDateClickCount == null)
        {
            strNowDateClickCount = nowDate + ":1";
        }
        else
        {
            int now_click = 0;
            string[] aStr = strNowDateClickCount.Split(':');
            if (aStr[0] == nowDate)
            {
                now_click = int.Parse(aStr[1]) + 1;
                strNowDateClickCount = nowDate + ":" + now_click;
            }
            else
            {
                strNowDateClickCount = nowDate + ":1";
            }
        }

        PlayerPrefs.SetString(flag_GDTBannerDateClickCount, strNowDateClickCount);
        Debug.Log(flag_GDTBannerDateClickCount + ": " + PlayerPrefs.GetString(flag_GDTBannerDateClickCount));
    }

    int GetGDTOpenBannerAdsCount()
    {
        int click_count = 0;

        string strNowDateClickCount = PlayerPrefs.GetString(flag_GDTBannerDateClickCount);
        Debug.Log("GDT Banner strNowDateClickCount :" + strNowDateClickCount.ToString());
        if (strNowDateClickCount == "" || strNowDateClickCount == null)
        {
            click_count = 0;
        }
        else
        {
            string nowDate = DateTime.Now.ToString("yyyy-MM-dd");
            string[] aStr = strNowDateClickCount.Split(':');
            if (aStr[0] == nowDate)
            {
                click_count = int.Parse(aStr[1]);
            }
            else
            {
                click_count = 0;
            }
        }

        return click_count;
    }


    public void DestroyNormalAd ()
		{
				if (IAppsTeamIOSUntil.IOSCheckContentUnLock (GlobalVariable.IAP_ID1))
						return;

                bannerBlackBg.SetActive(false);

                if (canShowGDTBanner)
                {
                    if (canShowBanner)
                    {
                        GDTMobBinding.hiddenBanner();
                    }
                        
                    return;
                }

				if (GlobalVariable.GetDeviceOSLanguage () == "sc") {		
						if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.BaiduMob) {
								if (canShowBanner)
										BaiduMobBinding.destroyBanner ();
						} else if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.GdtMob) {
								if (canShowBanner)
                                    GDTMobBinding.destroyBanner();
                        } else {
								if (canShowBanner)
                                    admobBannerView.Destroy();
						}	
				} else {
						if (canShowBanner)
                            admobBannerView.Destroy();
				}

		}

		public void ShowNormalAd ()
		{
				if (IAppsTeamIOSUntil.IOSCheckContentUnLock (GlobalVariable.IAP_ID1))
						return;

                if(canShowGDTBanner)
                {
                    if (canShowBanner)
                    {
                        GDTMobBinding.showBanner();
                    }
                    else
                    {
                         GDTMobBinding.destroyBanner(); 
                    }
                    return;
                }
		
				if (GlobalVariable.GetDeviceOSLanguage () == "sc") {

						if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.BaiduMob) {
								if (canShowBanner) {
										BaiduMobBinding.showBanner ();
								} else {
										BaiduMobBinding.hiddenBanner ();
								}
				
						} else if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.GdtMob) {
                                if (canShowBanner)
                                {
                                    GDTMobBinding.showBanner();
                                }
                                else
                                {
                                    GDTMobBinding.hiddenBanner();
                                }

                        } else {
								if (!canShowBanner)
										return;
                
                                if (canShowBanner)
                                {
                                    admobBannerView.Show();
                                }
                                else
                                {
                                    admobBannerView.Hide();
                                }
				
						}	
				} else {
						if (!canShowBanner)
								return;
            
                        if (canShowBanner)
                        {
                            admobBannerView.Show();
                        }
                        else
                        {
                            admobBannerView.Hide();
                        }
				}

		}

    /*
		public void HiddenNormalAd ()
		{
				if (IAppsTeamIOSUntil.IOSCheckContentUnLock (GlobalVariable.IAP_ID1))
						return;

                if(canShowGDTBanner)
                {
                    if (canShowBanner)
                        GDTMobBinding.hiddenBanner();

                    return;
                }
		
				if (GlobalVariable.GetDeviceOSLanguage () == "sc") {		
						if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.BaiduMob) {
								if (canShowBanner)
										BaiduMobBinding.hiddenBanner ();
						} else if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.GdtMob) {
								if (canShowBanner)
										GDTMobBinding.hiddenBanner ();
						} else {
								if (canShowBanner)
										admobBannerView.Hide ();
						}	
				} else {
						if (canShowBanner)
								admobBannerView.Hide ();
				}

		}
        */

        /*
    private void ShowGDT_InterstitialAd(UnifiedInterstitialAd ad, bool isFullScreen)
    {
        if (gdt_noVideoAd == null || !gdt_noVideoAd_isLoaded)
        {
            string msg = "请先加载广告:GDT_InterstitialAd";
            if (!gdt_noVideoAd_isLoaded)
            {
                msg = "等待广告加载完成:GDT_InterstitialAd";
            }
            
            Debug.Log(msg);
            return;
        }

        if (isFullScreen)
        {
            ad.ShowFullScreenAd();
        }
        else
        {
            ad.Show();
        }
    }
    */

    public void ShowInterstitialAd (string cacheName)
		{
				if (IAppsTeamIOSUntil.IOSCheckContentUnLock (GlobalVariable.IAP_ID1))
						return; 

				if (canShowInterstitialAds) {
						if (GlobalVariable.GetDeviceOSLanguage () == "sc")
                        {
                                //补余操作
								if (canShowAdmob) {
										if (IsAdmobInterstisialsAdReady) {
                                                admobInterstitial.Show();
										} else {
												print ("admob Interstitial is not ready yet.");
										}
										return;
								}
				
                                //补余操作
								if (canShowGDT)
                                {

                                    if (canShowNativeAds)
                                    {
                                        //if(GDTMob_NativeExpress_isReady) 
                                            //GDTMob_NativeExpress_Show();
                                        //else
                                            print("GDTMob NativeExpress is not ready yet.");
                                    }
                                    else
                                    {
                                        GDTMobBinding.displayInterstital();

                                    }	
									
                                    return;
								}
				
                                //非补余操作
								if (GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.BaiduMob)
                                {		
									BaiduMobBinding.displayInterstital ();
								}
                                //else if(GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.UnionMob)
                                //{
                                    //ShowUnion_NativeIntersititialAd();
                                //}
                                else if(GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.GdtMob) 
                                {	

                                    if (canShowNativeAds)
                                    {
                                        //if(GDTMob_NativeExpress_isReady) 
                                            //GDTMob_NativeExpress_Show();
                                        //else
                                            print("GDTMob NativeExpress is not ready yet.");
                                    }
                                    else
                                    {
                                            GDTMobBinding.displayInterstital();
                                    }   

										
								} 
                               else
                               {		
										if (IsAdmobInterstisialsAdReady) {
                                                admobInterstitial.Show();
										} else {
												print ("admob Interstitial is not ready yet.");
										}
								}
						} else {		
								if (IsAdmobInterstisialsAdReady) {
                                        admobInterstitial.Show();
								} else {
										print ("admob Interstitial is not ready yet.");
								}
						}
				}

                //if (canShowNativeAds) GDTMob_NativeExpress_Show();

		}

		public void CreateInterstitialAd_Baidumob ()
		{

				if (GlobalVariable.GetDeviceOSLanguage () == "sc")
                {

                    int click_limit = InterAds_BaiduClickCount();

                    if (GetBaiduOpenInterAdsCount() < click_limit)
                    {
                        BaiduMobBinding.requestInterstital (BaiduMob_ID, false, BaiduMob_AdUnitTag2);       
                    }
                    else
                    {
                        Debug.Log("today Baidu InterstitialAd have click over " + click_limit.ToString() + " time");
                    }
	
				}

		}

		public void CreateInterstitialAd_GDTmob ()
		{

            int click_limit = InterAds_GDTClickCount();

            if (GetGDTOpenInterAdsCount() < click_limit)
            {
                if (canShowNativeAds)
                {
                //GDTMob_CreateNativeExpressAdView();
                }
                else
                {
                    if (GlobalVariable.GetDeviceOSLanguage() == "sc")
                    {
                        GDTMobBinding.requestInterstital(GDT_Appkey, GDT_Banner2);

                    }
                }
            }
            else
            {
                Debug.Log("today GDT InterstitialAd have click over " + click_limit.ToString() + " time");
            }
         }


        void CreateInterstitialAd_Admob()
        {
            int click_limit = InterAds_AdmobClickCount();

            if (GetAdmobOpenInterAdsCount() < click_limit)
            {
            //admobInterstitial = new InterstitialAd(AdMob_Banner2);

            // Called when an ad request has successfully loaded.
            //admobInterstitial.OnAdLoaded += HandleInterstitialLoaded;
            // Called when an ad request failed to load.
            //admobInterstitial.OnAdFailedToLoad += HandleInterstitialFailedToLoad;
            // Called when an ad is shown.
            //admobInterstitial.OnAdOpening += HandleInterstitialOpened;
            // Called when the ad is closed.
            //admobInterstitial.OnAdClosed += HandleInterstitialClosed;
            // Called when the ad click caused the user to leave the application.
            //dmobInterstitial.OnAdLeavingApplication += HandleInterstitialLeftApplication;


            // Create an empty ad request.
            //AdRequest request = new AdRequest.Builder().Build();
            // Load the interstitial with the request.
            //admobInterstitial.LoadAd(request);

            IsAdmobInterstisialsAdReady = false;

            InterstitialAd.Load(AdMob_Banner2, new AdRequest(),
            (InterstitialAd ad, LoadAdError loadError) =>
            {
                if (loadError != null)
                {
                    Debug.Log("Interstitial ad failed to load with error: " +
                        loadError.GetMessage());
                    return;
                }
                else if (ad == null)
                {
                    Debug.Log("Interstitial ad failed to load.");
                    return;
                }

                Debug.Log("Interstitial ad loaded.");
                admobInterstitial = ad;

                ad.OnAdFullScreenContentOpened += () =>
                {
                    Debug.Log("Interstitial ad opening.");

                    HandleInterstitialOpened();
                };
                ad.OnAdFullScreenContentClosed += () =>
                {
                    Debug.Log("Interstitial ad closed.");
                    IsAdmobInterstisialsAdReady = false;
                    HandleInterstitialClosed();
                };
                ad.OnAdImpressionRecorded += () =>
                {
                    Debug.Log("Interstitial ad recorded an impression.");
                };
                ad.OnAdClicked += () =>
                {
                    Debug.Log("Interstitial ad recorded a click.");
                    IsAdmobInterstisialsAdReady = false;
                    HandleInterstitialLeftApplication();
                };
                ad.OnAdFullScreenContentFailed += (GoogleMobileAds.Api.AdError error) =>
                {
                    Debug.Log("Interstitial ad failed to show with error: " +
                                error.GetMessage());
                    IsAdmobInterstisialsAdReady = false;
                    HandleInterstitialFailedToLoad();
                };
                ad.OnAdPaid += (AdValue adValue) =>
                {
                    string msg = string.Format("{0} (currency: {1}, value: {2}",
                                               "Interstitial ad received a paid event.",
                                               adValue.CurrencyCode,
                                               adValue.Value);
                    Debug.Log(msg);
                };


            });


        }
            else
            {
                Debug.Log("today admob InterstitialAd have click over "+ click_limit.ToString() + " time");    
            }

		}

        void CreateInterstitialAd_UnionMob()
        {
            //LoadUnion_IntersititialAd();
        }

		public int GetAdsShowRate (string key, int default_count = 1)
		{
            int count = 1;
            count = default_count;

            string[] key_array = key.Split('.');

            if(key_array.Length == 2 && key != "")
            {
                count = GetIntFromConfig(key_array[0], key_array[1]);
            }
            else
            {
                Debug.LogWarningFormat("GetAdsShowRate has error for Key: {0}", key);
            }
           
            return count;
        }

    public int GetAdsShowRate_GDT_Inters(string key)
    {
        string strRate = String.Empty;
        #if UNITY_IPHONE
        strRate = IAppsTeamIOSUntil.GetConfigParams(key);
        #endif

        if (strRate == "" || strRate == null)
        {
            return 0;
        }
        else
        {
            int count = int.Parse(strRate);

            if (count < 0)
                count = 0;
            if (count > 100)
                count = 0;

            return count;
        }

    }


    public bool CanInterstitialAd()
    {
        string strType = "";
#if UNITY_IPHONE
        strType = IAppsTeamIOSUntil.GetConfigParams("p1");
#endif

        if (strType == "xxxx")//xxxx
        {
            return false;
        }
        else
        {
            return true;
        }

    }

    public bool GetCanPlanB()
    {
        string strType = "";
#if UNITY_IPHONE
        strType = IAppsTeamIOSUntil.GetConfigParams("PlanB");
#endif

        if (strType == "yes")
        {
            return true;
        }
        else
        {
            return false;
        }

    }

    public bool GetCanNativeAds()
        {
            string strType = "";
            #if UNITY_IPHONE
                strType = IAppsTeamIOSUntil.GetConfigParams("nativeAds");
            #endif

            if (strType == "xxxx")
            {
                //xxxx
                print("Not Can NativeAds");
                return false;
            }
            else
            {
                print("Can NativeAds");
                return true;
            }

        }

        public string AdsSwticher()
        {
            string strType = "";
            #if UNITY_IPHONE
            strType = IAppsTeamIOSUntil.GetConfigParams("AdsSwitch_admob");
            #endif
            //strType = "u2";
            if (strType == "u1")
            {
                Debug.Log("u1");
                return strType;
            }
            else if (strType == "u2")
            {
                Debug.Log("u2");
                return strType;
            }
            else
            {
                Debug.Log("c1");
                return "";
            } 
        }

    /*
        public string InterAdsNeedChange()
        {
            string strType = "";
            #if UNITY_IPHONE
            strType = IAppsTeamIOSUntil.GetConfigParams("InterAdsNeed");
            #endif
            Debug.Log("InterAdsNeedChange:" + strType);
            if (strType == "gd")
            {
                return strType;
            }
            else if(strType == "ad")
            {
                return strType;
            }
            else
            {
              return "0";
            }
        }
*/

        int GetIntFromConfig(string parent_key, string key)
        {
            try
            {
                string default_configData = Resources.Load(GlobalVariable.AdsConfig_Name).ToString();
                string config_data = PlayerPrefs.GetString(GlobalVariable.AdsConfig_Content, default_configData);

                JsonData data = JsonMapper.ToObject(config_data);

                string str_value = data[parent_key][key].ToString();

                int i_value = 1;
                int.TryParse(str_value, out i_value);
                return i_value;
            }
            catch
            {
                Debug.LogWarning(string.Format("GetIntFromConfig has error from key : {0}_{1}", parent_key, key));
                return 1;
            }
        }


        public int InterAds_AdmobClickCount()
        {
            /*
            string strType = "";
            #if UNITY_IPHONE
            strType = IAppsTeamIOSUntil.GetConfigParams("InterAdsAdmobClick");
            #endif
            */
            int aClick = 1;
            aClick = GetIntFromConfig("gads", "interads_DLimt");
            Debug.Log(string.Format("InterAdsAdmobClick: {0}", aClick.ToString()));
            return aClick;
        }

        public int InterAds_GDTClickCount()
        {
        
            int aClick = 1;
            aClick = GetIntFromConfig("gdt", "interads_DLimt");
            Debug.Log(string.Format("InterAds_GDTClickCount: {0}", aClick.ToString()));
            return aClick;
        }

        public int InterAds_BaiduClickCount()
        {
            string strType = "";
            #if UNITY_IPHONE
                strType = IAppsTeamIOSUntil.GetConfigParams("InterAdsBaiduClick");
            #endif
            int aClick = 1;
            Debug.Log("InterAdsBaiduClick:" + strType);
            if (strType == "" || strType == null)
            {
                return aClick;
            }
            else
            {
                if (int.TryParse(strType, out aClick))
                {
                    return aClick;
                }
            else
            {
                return 1;
            }
        }
    }

    public int BannerAds_AdmobClickCount()
    {
        /*
        string strType = "";
#if UNITY_IPHONE
        strType = IAppsTeamIOSUntil.GetConfigParams("BannerAdsAdmobClick");
#endif
        */
        int aClick = 1;

        aClick = GetIntFromConfig("gads", "banner_DLimt");
        Debug.Log(string.Format("BannerAdsAdmobClick: {0}", aClick.ToString()));
        return aClick;
    }

    public int BannerAds_GDTClickCount()
    {
        int aClick = 1;

        aClick = GetIntFromConfig("gdt", "banner_DLimt");
        Debug.Log(string.Format("BannerAds_GDTClickCount: {0}", aClick.ToString()));
        return aClick;
    }

    public int BannerAds_BaiduClickCount()
    {
        string strType = "";
#if UNITY_IPHONE
        strType = IAppsTeamIOSUntil.GetConfigParams("BannerAdsBaiduClick");
#endif
        int aClick = 1;
        Debug.Log("BannerAdsBaiduClick:" + strType);
        if (strType == "" || strType == null)
        {
            return aClick;
        }
        else
        {
            if (int.TryParse(strType, out aClick))
            {
                return aClick;
            }
            else
            {
                return 1;
            }

        }
    }

    public string BannerAdsReplaceAdmob()
    {
        /*
        string strType = "";
        #if UNITY_IPHONE
            strType = IAppsTeamIOSUntil.GetConfigParams("BannerAdsReplaceAdmob");
        #endif
        Debug.Log("BannerAdsReplaceAdmob:" + strType);
        if (strType == "baidu")
        {
            return strType;
        }
        else if (strType == "gdt")
        {
            return strType;
        }
        else
        {
            return "baidu";
        }
        */
        return "gdt";
    }


        /*
		#region Admob Native Ads Express
		public void Admob_CreatetNativeExpressAdView()
		{
				nativeExpressAdView_Admob = new NativeExpressAdView(Admob_NativeAdsUnitId, AdSize.MediumRectangle, AdPosition.Bottom);

				// Load a banner ad.
				nativeExpressAdView_Admob.LoadAd(new AdRequest.Builder()
						//.TagForChildDirectedTreatment(true)
						//.AddTestDevice("2077ef9a63d2b398840261c8221a0c9b")
						.Build());

				// Called when an ad request has successfully loaded.
				nativeExpressAdView_Admob.OnAdLoaded += AdmobNativeExpress_HandleOnAdLoaded;
				// Called when an ad request failed to load.
				nativeExpressAdView_Admob.OnAdFailedToLoad += AdmobNativeExpress_HandleOnAdFailedToLoad;
				// Called when an ad is clicked.
				//nativeExpressAdView.OnAdOpened += HandleOnAdOpened;
				// Called when the user returned from the app after an ad click.
				//nativeExpressAdView.OnAdClosed += HandleOnAdClosed;
				// Called when the ad click caused the user to leave the application.
				//nativeExpressAdView.OnAdLeavingApplication += HandleOnAdLeavingApplication;


		}

		public void AdmobNativeExpress_HandleOnAdLoaded(object sender, EventArgs args)
		{
				print("Admob NativeExpress Ad loaded");
				nativeExpressAdView_Admob.Hide ();
				Admob_NativeExpress_isReady = true;
		}

		public void AdmobNativeExpress_HandleOnAdFailedToLoad(object sender, AdFailedToLoadEventArgs args)
		{
				print("Admob NativeExpress Ad failed to load: " + args.Message);
				// Handle the ad failed to load event.
				Admob_NativeExpress_isReady = false;

				GDTMob_CreateNativeExpressAdView ();
		}

		public void Admob_NativeExpress_Show()
		{
				if(Admob_NativeExpress_isReady)
					nativeExpressAdView_Admob.Show();
		}


		public void Admob_NativeExpress_Destroy()
		{
				nativeExpressAdView_Admob.Destroy();
		}

		#endregion
		*/

		#region GDTMob Native Ads Express
        /*
		public void GDTMob_CreateNativeExpressAdView()
		{
				
				if (GlobalVariable.GetDeviceOSLanguage () == "sc") {	
						GDTMobBinding.CreateNativeExpressAdView (GDT_Appkey, GDT_NativeAdsUnitId, 300, 250, GDTMobNativeAdPosition.Center);
						GDTMobBinding.LoadNativeExpressAd();
				}

		}

		public void GDTMob_NativeExpress_Show()
		{
				if(GDTMob_NativeExpress_isReady)
					GDTMobBinding.ShowNativeExpressAdView();
		}

		public void GDTMob_NativeExpress_Destroy()
		{
				GDTMobBinding.DestroyNativeExpressAdView();
		}

		void GDT_NativeExpressAdSuccessToLoad (string empty)
		{
				//原生广告拉取成功
				Debug.Log ("GDT_NativeExpressAdSuccessToLoad");
				GDTMob_NativeExpress_isReady = true;
		}

		void GDT_NativeExpressAdFailToLoad (string empty)
		{
				//原生广告拉取失败
				Debug.Log ("原生广告拉取失败");
				GDTMob_NativeExpress_isReady = false;
				//Admob_CreatetNativeExpressAdView ();
                Debug.Log("GDT_NativeExpressAdFailToLoad - create admob");

                if (canShowAdmob)
                    return;
                
                canShowAdmob = true;
                CreateInterstitialAd_Admob();
		}

		void GDT_NativeExpressAdRenderFail (string empty)
		{
				//原生广告展示失败
				Debug.Log ("原生广告展示失败");
		}
    */

    void SwitchAds()
    {
        if (GlobalVariable.GetDeviceOSLanguage() == "sc")
        {
            GlobalVariable.showNoramlAdsCount++;

            if(GlobalVariable.showNoramlAdsCount >= normalAds_array.Length)
            {
                GlobalVariable.showNoramlAdsCount = 0;
            }

            //admob must Init;
            MobileAds.Initialize(initStatus => { });

            List<string> deviceIds = new List<string>();

            foreach (string devId in admob_testDevices)
            {
                deviceIds.Add(devId);
            }

            RequestConfiguration requestConfiguration = new RequestConfiguration();

            requestConfiguration.TestDeviceIds = deviceIds;
           

            MobileAds.SetRequestConfiguration(requestConfiguration);
            MobileAds.SetApplicationMuted(true);

            //baidu must init
            BaiduMobBinding.init(BaiduMob_ID, false, BaiduMob_cIDPermissionFlag);

            //GDTMobBinding must init
            GDTMobBinding.init(GDT_Appkey, GDT_Banner1);


            Debug.Log("====showNoramlAdsCount====: " + GlobalVariable.showNoramlAdsCount.ToString());

            string normalAdsType = normalAds_array[GlobalVariable.showNoramlAdsCount];

            Debug.Log("normalAdsType = " + normalAdsType);

            if(normalAdsType == GlobalVariable.NormalAdsMobType.Admob.ToString())
            {
                GlobalVariable.normalAdsMobType = GlobalVariable.NormalAdsMobType.Admob;
                Debug.Log("====1 switch Admob====");
            }
            else if (normalAdsType == GlobalVariable.NormalAdsMobType.BaiduMob.ToString())
            {
                GlobalVariable.normalAdsMobType = GlobalVariable.NormalAdsMobType.BaiduMob;
                Debug.Log("====2 switch BaiduMob====");
            }
            else if (normalAdsType == GlobalVariable.NormalAdsMobType.GdtMob.ToString())
            {
                GlobalVariable.normalAdsMobType = GlobalVariable.NormalAdsMobType.GdtMob;
                Debug.Log("====3 switch GdtMob====");
            }
            else
            {
                GlobalVariable.normalAdsMobType = GlobalVariable.NormalAdsMobType.Admob;
                Debug.Log("====5 switch Admob====");
            }

            if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.BaiduMob)
            {
                CreateNormalBanner_Baidumob();
            }
            else if (GlobalVariable.normalAdsMobType == GlobalVariable.NormalAdsMobType.GdtMob)
            {
                CreateNormalBanner_GDTmob();
            }
            else
            {
                CreateNormalBanner_Admob();
            }

            if (canShowInterstitialAds)
            {
                GlobalVariable.showInterstitialAdsCount++;

                if (GlobalVariable.showInterstitialAdsCount >= intersAds_array.Length)
                {
                    GlobalVariable.showInterstitialAdsCount = 0;
                }

                Debug.Log("====showInterstitialAdsCount====: " + GlobalVariable.showInterstitialAdsCount.ToString());

                string intersAdsType = intersAds_array[GlobalVariable.showInterstitialAdsCount];

                Debug.Log("intersAdsType = " + intersAdsType);

                if (intersAdsType == GlobalVariable.InterstitialAdsType.Admob.ToString())
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.Admob;
                    Debug.Log("====1 switch InterstitialAds Admob====");
                }
                else if (intersAdsType == GlobalVariable.InterstitialAdsType.BaiduMob.ToString())
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.BaiduMob;
                    Debug.Log("====2 switch InterstitialAds BaiduMob====");
                }
                else if (intersAdsType == GlobalVariable.InterstitialAdsType.GdtMob.ToString())
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.GdtMob;
                    Debug.Log("====3 switch InterstitialAds GdtMob====");
                }
                else if (intersAdsType == GlobalVariable.InterstitialAdsType.EmptyMob.ToString())
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.EmptyMob;
                    Debug.Log("====4 switch InterstitialAds EmptyMob====");
                }
                else
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.Admob;
                    Debug.Log("====5 switch InterstitialAds Admob====");
                }

                if (GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.BaiduMob)
                {
                    CreateInterstitialAd_Baidumob();
                }
                else if (GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.GdtMob)
                {
                    CreateInterstitialAd_GDTmob();
                }
                else if (GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.EmptyMob)
                {
                    Debug.Log("--- !!! not create interstitial Ads !!! ---");
                }
                else
                {
                    CreateInterstitialAd_Admob();
                }
            }

        }
        else
        {
            //admob must Init;
            MobileAds.Initialize(initStatus => { });

            List<string> deviceIds = new List<string>();

            foreach (string devId in admob_testDevices)
            {
                deviceIds.Add(devId);
            }

            RequestConfiguration requestConfiguration = new RequestConfiguration();
            requestConfiguration.TestDeviceIds = deviceIds;

            MobileAds.SetRequestConfiguration(requestConfiguration);
            MobileAds.SetApplicationMuted(true);

            CreateNormalBanner_Admob();


            //Interstitial Setting
            if (canShowInterstitialAds)
            {
                GlobalVariable.showInterstitialAdsCount++;

                if (GlobalVariable.showInterstitialAdsCount >= intersAds_array.Length)
                {
                    GlobalVariable.showInterstitialAdsCount = 0;
                }

                Debug.Log("====showInterstitialAdsCount====: " + GlobalVariable.showInterstitialAdsCount.ToString());

                string intersAdsType = intersAds_array[GlobalVariable.showInterstitialAdsCount];

                Debug.Log("intersAdsType = " + intersAdsType);

                if (intersAdsType == GlobalVariable.InterstitialAdsType.Admob.ToString())
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.Admob;
                    Debug.Log("====1 switch InterstitialAds Admob====");
                }
                else if (intersAdsType == GlobalVariable.InterstitialAdsType.EmptyMob.ToString())
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.EmptyMob;
                    Debug.Log("====2 switch InterstitialAds EmptyMob====");
                }
                else
                {
                    GlobalVariable.interstitialAdsType = GlobalVariable.InterstitialAdsType.Admob;
                    Debug.Log("====3 switch InterstitialAds Admob====");
                }

                if (GlobalVariable.interstitialAdsType == GlobalVariable.InterstitialAdsType.EmptyMob)
                {
                    Debug.Log("--- !!! not create interstitial Ads !!! ---");
                }
                else
                {
                    CreateInterstitialAd_Admob();
                }
            }

          
        }

    }




    //ByteDance.Union
    /*
    private AdNative AdNative
    {
        get
        {
            if (this.adNative == null)
            {
                this.adNative = SDK.CreateAdNative();
            }

            return this.adNative;
        }
    }

    private void LoadUnion_IntersititialAd()
    {
 
        if (this.intersititialAd != null)
        {
            Debug.LogError("广告已经加载");
            return;
        }

        var adSlot = new AdSlot.Builder()

            .SetCodeId("939143243")
            .SetSupportDeepLink(true)
            .SetImageAcceptedSize(600, 600)
            .SetNativeAdType(AdSlotType.InteractionAd)
            .SetAdCount(1)
            .Build();
             this.AdNative.LoadNativeAd(adSlot, new NativeAdListener(this));
    }

    private sealed class NativeAdListener : INativeAdListener
    {
        private AdsBannerControl example;

        public NativeAdListener(AdsBannerControl example)
        {
            this.example = example;
        }

        public void OnError(int code, string message)
        {
            Debug.LogError("OnNativeAdError: " + message);
        }

        public void OnNativeAdLoad(AndroidJavaObject list, NativeAd ad)
        {

            this.example.intersititialAd = ad;
            ad.SetNativeAdInteractionListener(
                new NativeAdInteractionListener(this.example)
            );

            Debug.Log("OnNativeAdLoad");

        }
    }

    private sealed class NativeAdInteractionListener : IInteractionAdInteractionListener
    {
        private AdsBannerControl example;

        public NativeAdInteractionListener(AdsBannerControl example)
        {
            this.example = example;
        }

        public void OnAdShow()
        {
            Debug.Log("NativeAd show");
        }

        public void OnAdClicked()
        {
            Debug.Log("NativeAd click");
        }

        public void OnAdDismiss()
        {
            Debug.Log("NativeAd close");
        }
    }

    public void ShowUnion_NativeIntersititialAd()
    {

        if (intersititialAd == null)
        {
            Debug.LogError("请先加载广告");
            return;
        }
        this.intersititialAd.ShowNativeAd();
    }
*/

    /*
     string GetAdsSwitch()
     {
         string strType = "old";

 #if UNITY_IPHONE
         strType = IAppsTeamIOSUntil.GetConfigParams("adsSwitch");
 #endif

         if (strType == "" || strType == null || strType == "old")
         {
             strType = "old";
         }
         else
         {
             strType = "new";
         }
         //strType = "new";
         return strType;
     }
     */
    #endregion


    
}
