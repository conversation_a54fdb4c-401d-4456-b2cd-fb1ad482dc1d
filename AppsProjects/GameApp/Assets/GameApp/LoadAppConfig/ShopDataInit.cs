using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Holoville.HOTween;
using Holoville.HOTween.Core;
using SA.IOSNative.StoreKit;



public class ShopDataInit : MonoBehaviour
{

    private string _errorMessage;
    private int actionId = 1; //1: buy, 2:restore

    enum ActionType
    {
        buy,
        restore,
        shop,
    }
    ActionType action = ActionType.shop;

    // Use this for initialization
    void Start()
    {

        PaymentManager.OnStoreKitInitComplete += OnStoreKitInitComplete;
        PaymentManager.OnRestoreComplete += OnRestoreComplete;


        if (!IAppsTeamIOSUntil.IOSCheckContentUnLock(GlobalVariable.IAP_ID1))
        {
            GetShopData();
        }
        else
        {
            Debug.Log("Not GetShopData");
        }

    }

    void GetShopData()
    {
        Debug.Log("GetShopData");
        action = ActionType.shop;
        PaymentManager.Instance.AddProductId(GlobalVariable.IAP_ProductId1);

        PaymentManager.Instance.LoadStore();
    }

   

    #region IAP



    void OnStoreKitInitComplete(SA.Common.Models.Result result)
    {

        //NativeDialogs.Instance.HideProgressDialog();
        if (result.IsSucceeded)
        {
            //IOSNativePopUpManager.showMessage("StoreKit Init Succeeded", "Available products count: " + IOSInAppPurchaseManager.instance.Products.Count.ToString());
            //Debug.Log("StoreKit Init Succeeded Available products count: " + IOSInAppPurchaseManager.instance.Products.Count.ToString());

            string strPrice = "", currencySymbol = "", currencyCode = "", priceInfo = "";
            // Do something more useful with the products than printing them to the console
            foreach (Product product in PaymentManager.Instance.Products)
            {
                //Debug.Log( product.ToString() + "\n" );
                priceInfo = product.CurrencySymbol + product.Price;
                Debug.Log("product.currencySymbol:" + product.CurrencySymbol);
                Debug.Log("product.CurrencyCode:" + product.CurrencyCode);
                Debug.Log("product.Price:" + product.Price.ToString());

                strPrice = product.Price.ToString();
                currencySymbol = product.CurrencySymbol;
                currencyCode = product.CurrencyCode;
            }

            if (action == ActionType.shop)
            {
                ShowShop(strPrice, currencySymbol, currencyCode, priceInfo);;
            }

        }
        else
        {
            string msg = "StoreKit Init Failed , Error code: " + result.Error.Code + " " + "Error description:" + result.Error.Message;
            Debug.Log(msg);
        }

        //RestoreAction();

    }

    void ShowShop(string strPrice, string currencySymbol, string currencyCode, string priceInfo)
    {

        string str = "";

        if (currencyCode == "CNY")
        {
            str = "¥" + strPrice;
        }
        else if (currencyCode == "USD")
        {
            str = "$" + strPrice;
        }
        else if (currencyCode == "AUD")
        {
            str = "$" + strPrice;
        }
        else if (currencyCode == "EUR")
        {
            str = strPrice + "€";
        }
        else if (currencyCode == "TWD")
        {
            str = "NT$" + strPrice;
        }
        else if (currencyCode == "HKD")
        {
            str = "HK$" + strPrice;
        }
        else if (currencyCode == "JPY")
        {
            str = "¥" + strPrice;
        }
        else if (currencyCode == "CAD")
        {
            str = "$" + strPrice;
        }
        else if (currencyCode == "SGD")
        {
            str = "S$" + strPrice;
        }
        else if (currencySymbol == "$")
        {
            str = currencySymbol + strPrice;
        }
        else if (currencySymbol == "¥")
        {
            str = currencySymbol + strPrice;
        }
        else if (currencySymbol == "€")
        {
            str = strPrice + currencySymbol;
        }
        else
        {
            str = currencyCode + " " + strPrice;
        }

        string savePriceFlag = GlobalVariable.SavePriceFlag;
        string defaultPrice = GlobalVariable.DefaultPrice;

        if (strPrice == "-0.99")
        {

        }
        else
        {
            PlayerPrefs.SetString(savePriceFlag, str);
        }
    }

    void RestoreAction()
    {
        PaymentManager.Instance.RestorePurchases();
        Debug.Log("shopInit_restoring..");
    }

    void OnDisable()
    {
        PaymentManager.OnStoreKitInitComplete -= OnStoreKitInitComplete;
        PaymentManager.OnRestoreComplete -= OnRestoreComplete;
    }


    void OnRestoreComplete(RestoreResult res)
    {
        if (res.IsSucceeded)
        {
            Debug.Log("shopInit_restoreTransactionsFinished");
            IAppsTeamIOSUntil.MobClickEvent("restore_buy1");
            UnlockProducts(GlobalVariable.IAP_ProductId1);
        }
        else
        {
            Debug.Log("shopInit_restoreTransactionsFailed: " + "Error: " + res.Error.Code + " " + res.Error.Message);
        }
    }

    void UnlockProducts(string productIdentifier)
    {
        Debug.Log("shopInit_purchased product: " + productIdentifier);
        IAppsTeamIOSUntil.MobClickEvent("success_buy1");
        IAppsTeamIOSUntil.AddCode(GlobalVariable.IAP_ID1);
    }

    #endregion
}

