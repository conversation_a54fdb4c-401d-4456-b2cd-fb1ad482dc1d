/** This is an automatically generated class by FairyGUI. Please do not modify it. **/

using FairyGUI;
using FairyGUI.Utils;

namespace FD1_UI.FD1_HomePage
{
    public partial class UI_BtnMoreApps : GComponent
    {
        public GImage m_bg;
        public GImage m_icon;
        public GLoader m_Img_moreapps;
        public const string URL = "ui://t2o72hgwn8gq1d";

        public static UI_BtnMoreApps CreateInstance()
        {
            return (UI_BtnMoreApps)UIPackage.CreateObject("FD1_HomePage", "BtnMoreApps");
        }

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            m_bg = (GImage)GetChildAt(1);
            m_icon = (GImage)GetChildAt(2);
            m_Img_moreapps = (GLoader)GetChildAt(3);
        }
    }
}