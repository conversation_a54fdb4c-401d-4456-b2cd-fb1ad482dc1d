//
//  IAppsTeamIOSUnitl.m
//  Unity-iPhone
//
//  Created by pwc on 12-12-18.
//
//

#import <SystemConfiguration/SystemConfiguration.h>
#include <netdb.h>
#import "IAppsTeamIOSUnitl.h"
#import "RSSecrets.h"

//#import "UMFeedback.h"


//#import "HJObjManager.h"
//#import "HJManagedImageV.h"

//#import "UMFeedbackViewController.h"
#import <sys/utsname.h>
#import "Reachability.h"


UIViewController *UnityGetGLViewController();
void UnityPause( int pause );


@implementation IAppsTeamIOSUnitl

@synthesize iap_ProductId, iap_SettingId, iap_flag;

+ (IAppsTeamIOSUnitl*)shared
{
    static IAppsTeamIOSUnitl *iAppsTeamIOSUnitl = nil;
    
    if( !iAppsTeamIOSUnitl )
        iAppsTeamIOSUnitl = [[IAppsTeamIOSUnitl alloc] init];
    
    return iAppsTeamIOSUnitl;
}


- (id)init
{
    if( ( self = [super init] ) )
    {

    }
    return self;
}



- (void)dealloc {
    
   
}




- (UIViewController*)getViewControllerForModalPresentation:(BOOL)destroyIfExists
{
    return UnityGetGLViewController();
}


- (BOOL)checkContentUnLock:(NSString*)productIdentifier
{
   
    
    //NSLog(@"productIdentifier: %@", productIdentifier);
    NSString *flag = [RSSecrets stringForKey:productIdentifier];
    //NSLog(@"flag: %@", flag);
    
    if ([flag isEqualToString:[NSString stringWithFormat:@"%@%@", productIdentifier, self.iap_flag]])
    {
        //NSLog(@"product is unlock: %@", productIdentifier);
        return true;
    }
    else
    {
        //NSLog(@"product is lock: %@", productIdentifier);
        return false;
    }


}

- (NSString *)checkUserOSLanguage
{
    NSString *userLanguage = @"en";
    float os = [self getSystemVersion];
    
    if (os >= 9.0f)
    {
        NSArray *languages = [[NSBundle mainBundle] preferredLocalizations];
        
        NSString *language = [[languages objectAtIndex:0] uppercaseString];
        
        //english:1, 繁体:2, 简体：3
        if([language isEqualToString:@"ZH-HANS"] || [language containsString:@"ZH-HANS"])//简体：3
        {
            userLanguage = @"sc";
        }
        else if([language isEqualToString:@"ZH-HANT"] || [language containsString:@"ZH-HANT"]
                || [language containsString:@"ZH-HK"]
                )//繁体:2
        {
            userLanguage = @"tc";
        }
        else
        {
            userLanguage = @"en";
        }
        
        //NSLog(@"localeIdentifier: %@", language);
        //NSLog(@"userLanguage: %@", userLanguage);;
        
    }
    else
    {
        NSArray *languages = [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"];
        NSString *language = [[languages objectAtIndex:0] uppercaseString];
        
        //english:1, 繁体:2, 简体：3
        if([language isEqualToString:@"ZH-HANS"])//简体：3
        {
            userLanguage = @"sc";
        }
        else if([language isEqualToString:@"ZH-HANT"])//繁体:2
        {
            userLanguage = @"tc";
        }
        else
        {
            userLanguage = @"en";
        }
        
        
        //NSLog(@"localeIdentifier: %@", language);
        //NSLog(@"userLanguage: %@", userLanguage);
    }
    
    return userLanguage;
}

- (void)mobClick_beginEvent:(NSString*)eventname
{
    //[MobClick beginEvent:eventname];
}

- (void)mobClick_endEvent:(NSString*)eventname
{
    //[MobClick endEvent:eventname];
}

- (void)mobClick_event:(NSString*)eventname
{
    //[MobClick event:eventname];
}

- (void)umeng_updateOnlineConfig
{
    //[MobClick updateOnlineConfig];
}

- (void)showFeedBack
{
    //UMFeedback *umFeedback = [UMFeedback sharedInstance];
    //[umFeedback setAppkey:UMeng_APPKEY delegate:nil];

    //[UMFeedback setLogEnabled:YES];
    
    //UMFeedbackViewController *feedbackViewController = [[UMFeedbackViewController alloc] initWithNibName:@"UMFeedbackViewController" bundle:nil];
    //feedbackViewController.appkey = UMeng_APPKEY;
    //UINavigationController *navigationController = [[UINavigationController alloc] initWithRootViewController:feedbackViewController];
    //navigationController.navigationBar.barStyle = UIBarStyleBlack;
    //navigationController.navigationBar.translucent = NO;
    //[UnityGetGLViewController() presentModalViewController:navigationController animated:YES];

}


- (BOOL)connectedToNetwork
{
    // Create zero addy
    struct sockaddr_in zeroAddress;
    bzero(&zeroAddress, sizeof(zeroAddress));
    zeroAddress.sin_len = sizeof(zeroAddress);
    zeroAddress.sin_family = AF_INET;
    
    // Recover reachability flags
    SCNetworkReachabilityRef defaultRouteReachability = SCNetworkReachabilityCreateWithAddress(NULL, (struct sockaddr *)&zeroAddress);
    SCNetworkReachabilityFlags flags;
    
    BOOL didRetrieveFlags = SCNetworkReachabilityGetFlags(defaultRouteReachability, &flags);
    CFRelease(defaultRouteReachability);
    
    if (!didRetrieveFlags)
    {
        printf("Error. Could not recover network reachability flags\n");
        return NO;
    }
    
    BOOL isReachable = ((flags & kSCNetworkFlagsReachable) != 0);
    BOOL needsConnection = ((flags & kSCNetworkFlagsConnectionRequired) != 0);
    return (isReachable && !needsConnection) ? YES : NO;
    
}

- (float)getSystemVersion
{
    return [[UIDevice currentDevice].systemVersion floatValue];
}

- (NSString *)getConfigParams:(NSString*)keyname
{
    ///return [MobClick getConfigParams:keyname];
}

- (void)loadOurAdsData:(float)pos_x pos_y:(float)pos_y width:(float)width height:(float)height
{
    
    float scale = [[UIScreen mainScreen] scale];
    if (scale > 1)
    {
        ourAds_pos_x = (pos_x-width)*0.5f;
        ourAds_pos_y = (pos_y-height)*0.5f;
    }
    else
    {
        ourAds_pos_x = pos_x - width*0.5f;
        ourAds_pos_y = pos_y - height*0.5f;
    }
    
    ourAds_width = width;
    ourAds_height = height;
    /*
    self.imgMan = [[[HJObjManager alloc] init] autorelease];
    NSString* cacheDirectory = [NSHomeDirectory() stringByAppendingString:@"/Library/Caches/OurAdsCache/"] ;
    HJMOFileCache* fileCache = [[[HJMOFileCache alloc] initWithRootPath:cacheDirectory] autorelease];
    self.imgMan.fileCache = fileCache;
    
    ourAdsCount = 0;
    currentAdsId = 0;
    ourAdsRefresh = 6;
    canAdsRefresh = true;
    
    myTimer =  [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(refreshAdsAction) userInfo:nil repeats:YES];
    
    
    
    if([self getConfigParams:@"OurAdsVersion1"] == nil
       || [[self getConfigParams:@"OurAdsVersion1"] isEqualToString:@"0"]
       || [[self getConfigParams:@"OurAdsVersion1"] isEqualToString:@""])
    {
        NSString *filePath = [[NSBundle mainBundle] pathForResource:@"OursAdsData" ofType:@"json"];
        
        NSData *data = [NSData dataWithContentsOfFile:filePath];
        self.ourAdsData = [[[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding] autorelease];
        ourAdsVersion = 0;
        //NSLog(@"aa:%@", self.ourAdsData);
        
    }
    else
    {
        self.ourAdsData = [self getConfigParams:@"OurAdsContent1"];
        ourAdsVersion = [[self getConfigParams:@"OurAdsVersion1"] integerValue];
    }
    //ourAdsVersion = 1;
    //self.ourAdsData = [self getConfigParams:@"OurAdsContent"];
    NSLog(@"ourAdsVersion:%d", ourAdsVersion);
    NSLog(@"ourAdsData:%@", self.ourAdsData);
    
    if([self.ourAdsData isEqualToString:@""] || self.ourAdsData == nil)
        return;
    @try
    {
        NSDictionary *data1 = [self.ourAdsData objectFromJSONString];
        
        NSString *Order = [data1 objectForKey:@"Order"];
        
        NSLog(@"Order: %@", Order);
        if(Order)
        {
            self.adsIds = [Order componentsSeparatedByString:@","];
            ourAdsCount = [self.adsIds count];
            [self CreateOurAdsOne];
        }
        
        
    }
    @catch (NSException * e)
    {
        
        NSLog(@"no data");
        /*
         UIAlertView *av = [[[UIAlertView alloc] initWithTitle:@"" message:@"数据在更新，请稍后再试试，谢谢！" delegate:nil cancelButtonTitle:@"确定" otherButtonTitles: nil] autorelease];
         [av show];
         */
        //return;
    //}
    
    
}

- (void)CreateOurAdsOne
{
    /*
    countSecond = 0;
    HJManagedImageV *mi = (HJManagedImageV *)[UnityGetGLViewController().view viewWithTag:6666];
    if(mi) [mi removeFromSuperview];
    
    NSString *imgUrl = @"";
    NSString *currentIdVaule = [self.adsIds objectAtIndex:currentAdsId];
    @try
    {
        NSDictionary *data1 = [self.ourAdsData objectFromJSONString];
        
        imgUrl = [data1 objectForKey:[NSString stringWithFormat:@"%@%@", currentIdVaule, self.languageUserFlag]];
        
        NSLog(@"imgUrl: %@", imgUrl);
        
        self.ourAdsAppId = [data1 objectForKey:[NSString stringWithFormat:@"Id%@", currentIdVaule]];
    }
    @catch (NSException * e)
    {
        
        NSLog(@"no data");

        return;
    }
    
    NSString *preloadImagePath = nil;
    if(ourAdsVersion == 0)
    {
        preloadImagePath = [[NSBundle mainBundle] pathForResource:imgUrl ofType:@"png"];
    }
    else
    {
        preloadImagePath = [[NSBundle mainBundle] pathForResource:@"loading" ofType:@"png"];
    }
    
    UIImage *preloadImage = [UIImage imageWithContentsOfFile:preloadImagePath];
    
    
    HJManagedImageV* mi1;
    mi1 = [[[HJManagedImageV alloc] initWithFrame:CGRectMake(ourAds_pos_x+ourAds_width,
                                                             ourAds_pos_y,
                                                             ourAds_width,
                                                             ourAds_height)] autorelease];
    mi1.image = preloadImage;
    mi1.tag = 6666;
    [UnityGetGLViewController().view addSubview:mi1];
    
    if(ourAdsVersion > 0) mi1.url = [NSURL URLWithString:[NSString stringWithFormat:@"%@",imgUrl]];
    [mi1 setCallbackOnImageTap:self method:@selector(imgClick:)];
    [self.imgMan manage:mi1];
    
    CGRect orgiFrame = mi1.frame;
    orgiFrame.origin.x = ourAds_pos_x;

    
    [UIView animateWithDuration:0.3
                          delay:0.1
                        options: UIViewAnimationCurveEaseOut
                     animations:^{
                         mi1.frame = orgiFrame;
                     }
                     completion:^(BOOL finished){
                         NSLog(@"Done!");
                     }];
    
    
    mi1.transform = CGAffineTransformMakeScale(0.75f, 0.75f);
    */
    
}

- (void) refreshAdsAction
{
    countSecond ++;
    NSLog(@"%d", countSecond);
    
    if(countSecond >= ourAdsRefresh)
    {
        if (canAdsRefresh) [self ChangeOurAds];
        countSecond = 0;
    }
}

- (void) imgClick:(id)sender
{
    
}

- (void)ChangeOurAds
{
    /*
    NSLog(@"ChangeOurAds");
    HJManagedImageV *mi = (HJManagedImageV *)[UnityGetGLViewController().view viewWithTag:6666];
    if(mi && canAdsRefresh)
    {
        currentAdsId ++;
        
        if(currentAdsId >= ourAdsCount) currentAdsId = 0;
        
        @try
        {
            NSString *currentIdVaule = [self.adsIds objectAtIndex:currentAdsId];
            NSDictionary *data1 = [self.ourAdsData objectFromJSONString];
            
            self.ourAdsAppId = [data1 objectForKey:[NSString stringWithFormat:@"Id%@", currentIdVaule]];
            
            
            
            NSString *imgUrl = [data1 objectForKey:[NSString stringWithFormat:@"%@%@", currentIdVaule, self.languageUserFlag]];
            
            if(ourAdsVersion == 0)
            {
                NSString *preloadImagePath = [[NSBundle mainBundle] pathForResource:imgUrl ofType:@"png"];
                UIImage *preloadImage = [UIImage imageWithContentsOfFile:preloadImagePath];
                [mi setImage:preloadImage];
            }
            
            if(ourAdsVersion > 0) mi.url = [NSURL URLWithString:[NSString stringWithFormat:@"%@",imgUrl]];
            [self.imgMan manage:mi];
            
        }
        @catch (NSException * e)
        {
            
            NSLog(@"no data");
     
            return;
        }
    }
*/
}


- (void)removeOurAds
{
    canAdsRefresh = false;
    countSecond = 0;
    [myTimer invalidate];
    myTimer = nil;
    /*
    HJManagedImageV *mi = (HJManagedImageV *)[UnityGetGLViewController().view viewWithTag:6666];
    if(mi) [mi removeFromSuperview];
     */
}

- (void)createOurAdsInterstitial:(NSString*)imgUrl isGetLocal:(bool) getLocal
{
    /*
    [self removeOurAdsInterstitial];
    
    NSString *preloadImagePath = nil;
    if(getLocal)
    {
        preloadImagePath = [[NSBundle mainBundle] pathForResource:imgUrl ofType:@"png"];
    }
    else
    {
        preloadImagePath = [[NSBundle mainBundle] pathForResource:@"bigLoading" ofType:@"png"];
    }
    
    UIImage *preloadImage = [UIImage imageWithContentsOfFile:preloadImagePath];
    
    float needScale = 0.85f;
    HJManagedImageV* mi2;
    
    CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;

    mi2 = [[[HJManagedImageV alloc] initWithFrame:CGRectMake(( screenWidth / 2 )- ( preloadImage.size.width *needScale/ 2 )-40 ,
                                                             ( screenHeight / 2 ) - ( preloadImage.size.height*needScale / 2 )-60,
                                                             preloadImage.size.width,
                                                             preloadImage.size.height)] autorelease];
    mi2.image = preloadImage;
    mi2.tag = 7777;
    [UnityGetGLViewController().view addSubview:mi2];
    
    if(!getLocal) mi2.url = [NSURL URLWithString:[NSString stringWithFormat:@"%@",imgUrl]];
    [mi2 setCallbackOnImageTap:self method:@selector(imgClickInterstitial:)];
    [self.imgMan manage:mi2];
    
    mi2.transform = CGAffineTransformMakeScale(needScale, needScale);
    //CGRect orgiFrame = mi2.frame;
    //orgiFrame.origin.x += 100;
    //mi2.frame = orgiFrame;
    */
}

- (void) imgClickInterstitial:(id)sender
{
    //UnitySendMessage( "OurAdsInterstitialControl", "adViewClicked", "");
}

- (void)removeOurAdsInterstitial
{
    /*
    HJManagedImageV *mi = (HJManagedImageV *)[UnityGetGLViewController().view viewWithTag:7777];
    if(mi) [mi removeFromSuperview];
     */
}

- (void)addCodeCont:(NSString *)key_name
{
    //NSLog(@"key_name: %@", key_name);
    [IAppsTeamIOSUnitl shared].iap_SettingId = key_name;
    
    //NSLog(@"iap_flag: %@", [IAppsTeamIOSUnitl shared].iap_flag);
    NSString *flagValue = [NSString stringWithFormat:@"%@%@", [IAppsTeamIOSUnitl shared].iap_SettingId, [IAppsTeamIOSUnitl shared].iap_flag];
    
    [RSSecrets setString:flagValue forKey:key_name];
    //NSLog(@"key: %@", [IAppsTeamIOSUnitl shared].iap_SettingId);
}

- (void)addCode:(SKProduct*)product
{
    //[self addCode];
    
    if (@available(iOS 11.0, *)) {
        [[SKProductStorePromotionController defaultController] updateStorePromotionVisibility: SKProductStorePromotionVisibilityHide forProduct:product
            completionHandler:^(NSError *error) {
                
                NSLog(@"updateStorePromotionVisibility_error: %@", error.description);
                                                                            }];
    } else {
        // Fallback on earlier versions
        NSLog(@"not support updateStorePromotionVisibility");
    }
}

- (void)requestReview
{
    if (@available(iOS 10.3, *)) {
         [SKStoreReviewController requestReview];
    }
}

- (BOOL)isIphoneX
{
    /*
    static BOOL isiPhoneX = NO;
    static dispatch_once_t onceToken;
    
    dispatch_once(&onceToken, ^{
#if TARGET_IPHONE_SIMULATOR
        NSString *model = NSProcessInfo.processInfo.environment[@"SIMULATOR_MODEL_IDENTIFIER"];
#else
        
        struct utsname systemInfo;
        uname(&systemInfo);
        
        NSString *model = [NSString stringWithCString:systemInfo.machine
                                             encoding:NSUTF8StringEncoding];
#endif
        isiPhoneX = [model isEqualToString:@"iPhone10,3"] || [model isEqualToString:@"iPhone10,6"];
    });
    
    return isiPhoneX;
    */
    if (@available(iOS 11, *)) {
        
        bool IsLandscape;
        UIInterfaceOrientation orientation = [UIApplication sharedApplication].statusBarOrientation;
        if(orientation == UIInterfaceOrientationLandscapeLeft || orientation == UIInterfaceOrientationLandscapeRight) {
            IsLandscape = true;
        } else {
            IsLandscape = false;
        }
        
        UIEdgeInsets insets = [UIApplication sharedApplication].delegate.window.safeAreaInsets;
        
        if(IsLandscape)
        {
            if (insets.left  > 0) {
                return YES;
            }
        }
        else
        {
            if (insets.top > 0) {
                return YES;
            }
        }
    }
    return NO;
    
}

- (void)unityPause:(bool)flag
{
    if(flag)
        UnityPause( 1 );
    else
        UnityPause( 0 );
    
}

- (void)setAudioSessionCategory
{

    AVAudioSession *session = [AVAudioSession sharedInstance];
    
    NSError *setCategoryError = nil;
    if (![session setCategory:AVAudioSessionCategoryPlayback
                  withOptions:AVAudioSessionCategoryOptionMixWithOthers
                        error:&setCategoryError]) {
        // handle error
    }

}

- (BOOL)isIpad
{
    if ( UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad )
    {
        return YES;
    }
    else
    {
        return NO;
    }
}

- (BOOL)isViaWiFi
{
    Reachability* reach = [Reachability reachabilityForInternetConnection];

    return reach.isReachableViaWiFi;
}

- (NSString *)webviewAdjustedRect:(float)top left:(float)left bottom:(float)bottom right:(float)right
                            point_x:(float)point_x point_y:(float)point_y width:(float)width height:(float)height
{
    /*
    //top = 160;
    top = 210;
    left = 0;
    bottom = 0;
    right = 0;
    point_x = 0;
    point_y = 0;
    
    NSLog(@"height: %f", height);
    */

    NSLog(@"top: %f", top);
    CGRect originalRect = CGRectMake(point_x, point_y, width, height);
    UIEdgeInsets insets = UIEdgeInsetsMake(top, left, bottom, right);
    CGRect adjustedRect = UIEdgeInsetsInsetRect(originalRect, insets);
    return NSStringFromCGRect(adjustedRect);
}

- (BOOL)checkIpadHasHomeIndicator
{
    
    if (@available(iOS 11, *)) {
                        
        UIEdgeInsets insets = [UIApplication sharedApplication].delegate.window.safeAreaInsets;
        
        NSLog(@"insets.top: %f", insets.top);
        NSLog(@"insets.bottom: %f", insets.bottom);
        NSLog(@"insets.left: %f", insets.left);
        NSLog(@"insets.right: %f", insets.right);
        
        if (insets.top == 0 && insets.bottom == 0 && insets.left == 0 && insets.right == 0)
        {
            return NO;
        }
        else if (insets.top == 0 && (insets.bottom > 0 || insets.bottom < 0 ))
        {
            return YES;
        }
        else
        {
            return NO;
        }
    }
    return NO;
}
@end
