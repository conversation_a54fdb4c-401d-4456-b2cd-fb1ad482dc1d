{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {"os": "Unix 15.5.0", "cpuCount": "12", "mappedPhysicalMemory": "59mb", "commandLine": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/build/deploy_arm64/il2cpp.dll --compile-cpp --platform=iOS --baselib-directory=/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Libraries --additional-defines=IL2CPP_DEBUG=0 --incremental-g-c-time-slice=3 --dotnetprofile=unityaot-macos --profiler-report --print-command-line --external-lib-il2-cpp=/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Libraries/libil2cpp.a --generatedcppdir=Il2CppOutputProject/Source/il2cppOutput --architecture=arm64 --outputpath=/Users/<USER>/Library/Developer/Xcode/DerivedData/Unity-iPhone-dyhgmxgrqnjlwwdtnzzbgoelbzrk/Build/Products/ReleaseForRunning-iphoneos/libGameAssembly.a --cachedirectory=/Users/<USER>/Library/Developer/Xcode/DerivedData/Unity-iPhone-dyhgmxgrqnjlwwdtnzzbgoelbzrk/Build/Intermediates.noindex/Unity-iPhone.build/ReleaseForRunning-iphoneos/artifacts/arm64 --configuration=Release"}, "traceEvents": [{"pid": 20369, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "il2cpp_outer"}}, {"pid": 20369, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 20369, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen2"}}, {"pid": 20369, "tid": 1, "ts": 1752432200796214, "dur": 1252, "ph": "X", "name": "GC - Gen2", "args": {}}, {"pid": 20369, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen1"}}, {"pid": 20369, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "GC - Gen0"}}, {"pid": 20369, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 20369, "tid": 1, "ts": 1752432195009783, "dur": 5767236, "ph": "X", "name": "il2cpp.exe", "args": {"analytics": "1"}}, {"pid": 20369, "tid": 1, "ts": 1752432195010929, "dur": 36375, "ph": "X", "name": "ParseArguments", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432195047305, "dur": 5037, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432195061401, "dur": 5707289, "ph": "X", "name": "BeeDriverRunner.ExecuteIl2Cpp", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432200771362, "dur": 4394, "ph": "X", "name": "Write Analytics", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432200777020, "dur": 12081, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {}}, {"pid": 20369, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20369, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20369, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": "Main thread"}}, {"pid": 20369, "tid": 1, "ts": 1752432195126398, "dur": 5636783, "ph": "X", "name": "Build FinalProgram", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432195127655, "dur": 6160, "ph": "X", "name": "Writing build program input data", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432195133830, "dur": 5629288, "ph": "X", "name": "Running build system backend", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432200763142, "dur": 39, "ph": "X", "name": "Finishing", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432200768568, "dur": 1134, "ph": "X", "name": "Main thread", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1752432195174667, "dur": 2176, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752432195176981, "dur": 6500, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752432195183523, "dur": 157, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752432195183688, "dur": 693, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752432195184382, "dur": 5562200, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752432200746694, "dur": 8640, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 0, "ts": 1752432200755335, "dur": 391, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "SaveScanCache"}}, {"pid": 12345, "tid": 1, "ts": 1752432195183584, "dur": 804, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195184394, "dur": 14097, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195198525, "dur": 208, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195198733, "dur": 7924, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195207236, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodHash.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195207439, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/Win32/ThreadLocalValueImpl.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195207000, "dur": 727, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195198524, "dur": 9203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/mghjhso6j3yo.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195207776, "dur": 161, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195207940, "dur": 334, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195207741, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kpghxehos6pk.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195208296, "dur": 125, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195208425, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195208285, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lx1us9itox58.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195208721, "dur": 361, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195209104, "dur": 385, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195208704, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2580dybsmckc.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195209530, "dur": 191, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195209731, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195210021, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_CappedSemaphore.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195209728, "dur": 531, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195209513, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fbcw9p75hl8e.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195210310, "dur": 221, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__15.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195210539, "dur": 433, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195210287, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wvlj6a5f9nm2.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195210981, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195211056, "dur": 168, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195211228, "dur": 352, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195210979, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4cr2a3hpwl60.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195211601, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195211952, "dur": 412, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195211587, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/girqjvjqkj6p.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195212397, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195212467, "dur": 254, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195212729, "dur": 322, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195212395, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rfsc423jh594.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195213077, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195213127, "dur": 287, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195213425, "dur": 364, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195213076, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qgfx4mgdkpgb.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195213829, "dur": 137, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195213970, "dur": 244, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195213802, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/aqrymakn7ndj.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195214270, "dur": 135, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195214411, "dur": 202, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195214243, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oxzsjb7vtkjv.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195214631, "dur": 139, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195214774, "dur": 209, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195214619, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ne7p58kme293.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195215026, "dur": 215, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195215452, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextHash.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195215246, "dur": 339, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195215004, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gt62hzxbrp39.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195215638, "dur": 220, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195215865, "dur": 320, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195215604, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fpug9kc47vdj.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195216186, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195216263, "dur": 191, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195216464, "dur": 378, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195216237, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/16sesgqoqfk9.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195216843, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195216935, "dur": 308, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195217427, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_FutexBased.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195217253, "dur": 362, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195216918, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cqfs7qr79v2p.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195217627, "dur": 296, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195217935, "dur": 245, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195217620, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yfcoeoryjgzl.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195218217, "dur": 97, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195218352, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Object.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195218318, "dur": 387, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195218197, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rhl9fuz9o8o4.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195218715, "dur": 224, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195219246, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195218945, "dur": 535, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195218710, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ig82r7tm5f50.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195219490, "dur": 178, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__8.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195219675, "dur": 431, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195219484, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ph0fghcj7sde.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195220160, "dur": 129, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195220293, "dur": 243, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195220130, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wqli0t8tqogx.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195220557, "dur": 132, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195220695, "dur": 323, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195220547, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a3xd04dyjyny.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195221072, "dur": 275, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__8.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195221352, "dur": 469, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195221045, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wlisyx11e2xx.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195221887, "dur": 247, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195222138, "dur": 201, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195221843, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3u3uiaycyx0g.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195222398, "dur": 309, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__14.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195222782, "dur": 373, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195222379, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/184li3wgjqv7.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195223207, "dur": 106, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195223317, "dur": 205, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195223163, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h7fqil3gr77h.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195223545, "dur": 89, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Numerics.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195223639, "dur": 260, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195223527, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1hegkc7m9owv.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195223944, "dur": 116, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__7.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195224254, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195224064, "dur": 309, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195223913, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4c3ndh46rv24.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195224423, "dur": 272, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195224699, "dur": 259, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195224399, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/x9bvxivdihym.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195224994, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195225044, "dur": 145, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195225315, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/ArchitectureDetection.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195225442, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/ExceptionSupportStack.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195225195, "dur": 485, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195224993, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/q6k2hkmk82t4.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195225893, "dur": 336, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195226410, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/vm/PlatformInvoke.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195226497, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/ArchitectureDetection.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195226693, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/NonCopyable.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195226777, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/dynamic_array.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195226871, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195226258, "dur": 748, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195225689, "dur": 1317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4aar5o3rbm8c.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195227042, "dur": 157, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195227391, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-common.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195227517, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Internal/Baselib_Lock_FutexBased.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195227208, "dur": 466, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195227024, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dkhp73s82kmp.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195227745, "dur": 217, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195228070, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include/C/Baselib_ErrorState.inl.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195227968, "dur": 445, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195227716, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/eeaas0c7hhtp.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195228479, "dur": 562, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195229183, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/Memory.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195229405, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195229813, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformDetection.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195229054, "dur": 908, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195228452, "dur": 1511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7ajhc23d82o7.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195229977, "dur": 331, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__6.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195230315, "dur": 240, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195229969, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dec2tk6zu9hw.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195230572, "dur": 117, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__23.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195230702, "dur": 324, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195230567, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/w0hkphag5hlr.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195231082, "dur": 129, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195231215, "dur": 231, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195231038, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lvhlycj9h71u.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195231478, "dur": 736, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__18.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195232220, "dur": 269, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195231452, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ipwywhoojhjd.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195232503, "dur": 190, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__13.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195232712, "dur": 246, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195232493, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pyh1gukz2cmw.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195232986, "dur": 314, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__10.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195233401, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/Posix/ThreadLocalValueImpl.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195233662, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorState.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195233304, "dur": 461, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195232972, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ivq80vb6syhe.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195233828, "dur": 100, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/LitJson.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195233932, "dur": 230, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195233800, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3jlwwdltlh11.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195234192, "dur": 120, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppInvokerTable.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195234318, "dur": 247, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195234176, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/aeaxp5uh2yht.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195234592, "dur": 139, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195234736, "dur": 223, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195234579, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/e90n6quve3si.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195234973, "dur": 97, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195235074, "dur": 193, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195234962, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vu3t995f2x5z.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195235275, "dur": 138, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/HOTween__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195235420, "dur": 219, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195235271, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zxwypxt950ej.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195235647, "dur": 167, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Ump.iOS.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195235818, "dur": 152, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195235643, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qm71xewg3l9b.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195235987, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195236081, "dur": 167, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195235974, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/psl7qh13zy0a.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195236258, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Common.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195236347, "dur": 162, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195236254, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6l8cwoy9vec1.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195236560, "dur": 235, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__9.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195236799, "dur": 147, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195236513, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hwuudbq20wue.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195236947, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195237015, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__71.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195237068, "dur": 351, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__71.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195237423, "dur": 146, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195237014, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oemfrpau1drn.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195237577, "dur": 106, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__7.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195237687, "dur": 156, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195237573, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/v86pdmv6t61f.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195237860, "dur": 141, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__68.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195238007, "dur": 208, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195237846, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gy75t8gxlpcc.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195238231, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__66.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195238320, "dur": 213, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195238226, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a30typo2fdp9.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195238542, "dur": 122, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__64.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195238668, "dur": 194, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195238537, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ofdpy0t68mwa.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195238913, "dur": 159, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__62.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195239076, "dur": 282, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195238899, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2rpkybza2w3b.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195239369, "dur": 286, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__60.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195239863, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195239661, "dur": 400, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195239362, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g41fym4ev6ad.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195240157, "dur": 240, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__58.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195240401, "dur": 173, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195240151, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9v7mllrgjkdh.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195240592, "dur": 127, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__55.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195240722, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195240578, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2sgu5min958y.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195240893, "dur": 89, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__53.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195240986, "dur": 153, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195240883, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g5sp5xnr2fg0.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195241150, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__51.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195241244, "dur": 158, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195241146, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/aamdafvww3sx.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195241409, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195241499, "dur": 153, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195241405, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9kjldx0ang9g.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195241659, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__48.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195241751, "dur": 154, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195241655, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2t73cy1zgfem.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195241917, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__46.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195242008, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195241913, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g6jzc9639oln.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195242168, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__44.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195242259, "dur": 240, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195242160, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/uiwuckc82yso.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195242525, "dur": 245, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__42.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195242794, "dur": 362, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195242505, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/onpih1jdxm5l.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195243165, "dur": 109, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__4.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195243298, "dur": 236, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195243160, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o5fxxnmdksxx.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195243553, "dur": 97, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__38.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195243655, "dur": 226, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195243546, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vr7k4acjeafr.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195243888, "dur": 175, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__37.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195244069, "dur": 163, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195243884, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ujn4jvvka8xb.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195244247, "dur": 99, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__35.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195244349, "dur": 185, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195244237, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/8vzzj60o3h5c.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195244560, "dur": 106, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__34.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195244990, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Assert.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195244671, "dur": 476, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195244538, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ngwb4wmr0ttt.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195245158, "dur": 153, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__31.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195245458, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_Atomic_TypeSafe.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195245388, "dur": 327, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195245151, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gd5ioydyqfpb.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195245767, "dur": 124, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__3.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195245894, "dur": 159, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195245727, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nxvgd85egqgi.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195246075, "dur": 104, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__28.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195246214, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringViewUtils.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195246184, "dur": 351, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195246059, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/8wq9qhk1ara0.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195246545, "dur": 99, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__26.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195246647, "dur": 200, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195246540, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/21jxvyr64enx.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195246857, "dur": 119, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__25.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195246981, "dur": 210, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195246852, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1tzgbjb80c6h.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195247209, "dur": 92, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__23.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195247304, "dur": 158, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195247196, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/f6bcbugcumdj.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195247469, "dur": 87, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__21.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195247560, "dur": 145, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195247465, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9a5zfbohoaqg.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195247715, "dur": 87, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195247808, "dur": 146, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195247711, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2hssxyrhd24y.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195247961, "dur": 143, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__18.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195248138, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195248108, "dur": 318, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195247957, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1uqqiuuk8lb5.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195248439, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__15.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195248533, "dur": 192, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195248432, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/urzx2vlry76m.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195248739, "dur": 99, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__13.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195248966, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195248843, "dur": 401, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195248732, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/83bt26qvrheo.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195249260, "dur": 100, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195249364, "dur": 146, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195249249, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/mgoo2hwzkrlp.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195249524, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__1.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195249613, "dur": 220, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195249514, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h2o4hpdkaetf.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195249844, "dur": 87, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__9.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195249934, "dur": 211, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195249837, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/y45dklym4iwc.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195250202, "dur": 339, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__7.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195250675, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-il2cpp.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195250545, "dur": 428, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195250195, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/chi9jw3qxs3d.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195250984, "dur": 251, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__5.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195251239, "dur": 320, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195250977, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qtu4j78uq2af.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195251621, "dur": 129, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__2.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195251755, "dur": 244, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195251585, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jq3b48z1go6w.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195252012, "dur": 178, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__16.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195252200, "dur": 364, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195252003, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/encivhuxj0qj.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195252609, "dur": 351, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__13.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195253000, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_SystemSemaphore.h"}}, {"pid": 12345, "tid": 1, "ts": 1752432195252964, "dur": 204, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195252578, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7jlpfjl49lm1.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195253173, "dur": 4514, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195257687, "dur": 1230, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__11.cpp"}}, {"pid": 12345, "tid": 1, "ts": 1752432195258920, "dur": 152, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1752432195253172, "dur": 5900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lwxkfuq82vt2.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432195259958, "dur": 2674200, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/lwxkfuq82vt2.o"}}, {"pid": 12345, "tid": 1, "ts": 1752432197934225, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Lib_iOS_arm64 z7ln/libGameAssembly.a"}}, {"pid": 12345, "tid": 1, "ts": 1752432197934913, "dur": 2741822, "ph": "X", "name": "Lib_iOS_arm64", "args": {"detail": "z7ln/libGameAssembly.a"}}, {"pid": 12345, "tid": 1, "ts": 1752432200676826, "dur": 69622, "ph": "X", "name": "CopyFiles", "args": {"detail": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Unity-iPhone-dyhgmxgrqnjlwwdtnzzbgoelbzrk/Build/Products/ReleaseForRunning-iphoneos/libGameAssembly.a"}}, {"pid": 12345, "tid": 2, "ts": 1752432195183589, "dur": 808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195184402, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195184479, "dur": 14099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195198579, "dur": 394, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195198973, "dur": 1160, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195200309, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195200478, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195200293, "dur": 395, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195198578, "dur": 2110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pj2yzww8d33m.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195200689, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195200782, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195200869, "dur": 269, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195201306, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195201491, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-sanitizers.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195201301, "dur": 353, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195200781, "dur": 873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kclv6h8is24h.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195201655, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195201746, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195201827, "dur": 440, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195202397, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195202715, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-sanitizers.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195202358, "dur": 533, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195201746, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cajmihkq9ba1.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195202893, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195202973, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195203064, "dur": 439, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195203525, "dur": 55, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195202971, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oga66rwwufv6.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195203621, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195203694, "dur": 363, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195204081, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195204296, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195204074, "dur": 384, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195203621, "dur": 838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tkhxy2cjvl8b.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195204460, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195204535, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195204608, "dur": 443, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195205111, "dur": 64, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool"}}, {"pid": 12345, "tid": 2, "ts": 1752432195205212, "dur": 383, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195204535, "dur": 1061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yt0l8h59e4fr.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195205611, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodTable.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195205672, "dur": 71, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodTable.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195205902, "dur": 197, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195205610, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gdlps9jq0rwb.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195206184, "dur": 212, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Ump_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195206439, "dur": 290, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195206146, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7jsvmi17to3z.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195206778, "dur": 986, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195207764, "dur": 1323, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 2, "ts": 1752432195206777, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ee55hmnaw85g.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195209540, "dur": 259396, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/ee55hmnaw85g.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195468982, "dur": 126, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__9.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195469112, "dur": 165, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195468976, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bvtfjlvvlk84.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195469285, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__8.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195469376, "dur": 189, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195469281, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qfqr3bhyiwwl.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195469575, "dur": 84, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__7.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195469663, "dur": 241, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195469570, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/p75ajw0zeuf6.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195469918, "dur": 104, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__6.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195470207, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/Time.h"}}, {"pid": 12345, "tid": 2, "ts": 1752432195470025, "dur": 402, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195469910, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4r2m3mm2b63m.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195470438, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__5.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195470532, "dur": 143, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195470432, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jczynd848hs3.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195470683, "dur": 301, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__4.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195470988, "dur": 142, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195470679, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/i4ei3xr64fbo.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471141, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__3.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471230, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195471136, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xobunod91rz4.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471372, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__2.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471460, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195471368, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wgrd38waypip.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471602, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471688, "dur": 140, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195471597, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/b1npnzidv165.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471837, "dur": 134, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195471975, "dur": 195, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195471833, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/spxqt462g19g.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195472179, "dur": 91, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/DOTween__1.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195472274, "dur": 156, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195472174, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7osfjv7iqaxp.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195472439, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/DOTweenPro.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195472528, "dur": 152, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195472434, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zl0fhhmnni2g.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195472689, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/DOTween.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195472778, "dur": 155, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195472684, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/s2me4ymdx9ip.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195472942, "dur": 150, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__9.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195473096, "dur": 207, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195472937, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hxws90rn5qhh.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195473322, "dur": 98, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__8.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195473424, "dur": 139, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195473313, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wht4trdq225x.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195473572, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__7.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195473663, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195473567, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/b2pgeizszeue.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195473808, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__6.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195473894, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195473804, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/au5zt2iuvcdz.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474036, "dur": 84, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__5.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474123, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195474032, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pe2bet4wso1f.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474265, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__46.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474352, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195474261, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k8y2lzhli0y6.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474511, "dur": 84, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__45.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474598, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195474491, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j0em1j1meyhr.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474755, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__44.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474840, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195474739, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ykbylanpba67.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195474989, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__43.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475075, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195474979, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d579508r8luo.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475223, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__42.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475310, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195475215, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cxntllst4jd9.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475456, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__41.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475542, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195475447, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rhk55bew1v1p.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475690, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__40.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475777, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195475680, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/q9zolwxxxtka.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195475919, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__4.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476004, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195475915, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4yynyjqzp0pw.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476145, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__39.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476230, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195476140, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/k9pcta1xp93t.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476379, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__38.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476464, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195476369, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j15v8ukzl7me.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476613, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__37.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476699, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195476603, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yl27sl61ijbv.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476851, "dur": 81, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__36.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195476936, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195476838, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xdir85p3ehuf.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477084, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__35.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477170, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195477075, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cye3swb6btiw.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477327, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__34.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477413, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195477308, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ribfdnx8846c.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477560, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__33.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477646, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195477551, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qarys7ga52px.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477788, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__32.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195477873, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195477784, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5unady2c2eee.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478020, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__31.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478106, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195478011, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kfkmxoofzq2u.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478255, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__30.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478341, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195478245, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j7z5c97gvolf.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478490, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__3.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478577, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195478480, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3qe7d490ly8g.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478723, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__29.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478808, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195478714, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xe90fh9fmqz2.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195478950, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__28.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479036, "dur": 133, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195478946, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cz5c07uij2nj.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479183, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__27.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479270, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195479174, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rj2okyglgec0.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479416, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__26.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479504, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195479408, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qbi8zi0mccvk.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479654, "dur": 84, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__25.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479741, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195479644, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5vekk9lp9nj1.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479889, "dur": 82, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__24.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195479974, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195479879, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4nu3zt5q5l2l.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195480120, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__23.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195480206, "dur": 134, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195480112, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j8qfkkrt2xq2.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195480353, "dur": 81, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__22.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195480437, "dur": 135, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195480344, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ysnr4adwz9ej.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195480588, "dur": 83, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__21.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195480674, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195480576, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xk3akvwxv7x3.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195480815, "dur": 1656, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__20.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195482472, "dur": 656, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__20.cpp"}}, {"pid": 12345, "tid": 2, "ts": 1752432195483132, "dur": 164, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1752432195480814, "dur": 2483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/c5zm4li0sjmk.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432195484039, "dur": 1107388, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/c5zm4li0sjmk.o"}}, {"pid": 12345, "tid": 2, "ts": 1752432196591507, "dur": 4154982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195183593, "dur": 815, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195184411, "dur": 14154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195198566, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195198972, "dur": 1178, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195200295, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195198565, "dur": 1994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9o6j2vb503wg.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195200610, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195200675, "dur": 539, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195201655, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 3, "ts": 1752432195201909, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 3, "ts": 1752432195201354, "dur": 615, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195200608, "dur": 1362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yfp2ugaomwuq.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195201970, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195202125, "dur": 236, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195202778, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 3, "ts": 1752432195202630, "dur": 436, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195202081, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hsjway490hgg.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195203067, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195203163, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195203235, "dur": 304, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Internal_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195203700, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 3, "ts": 1752432195203776, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 3, "ts": 1752432195203589, "dur": 478, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195203161, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vtwe3zpglaoa.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195204110, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195204198, "dur": 259, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195204665, "dur": 51, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 3, "ts": 1752432195204716, "dur": 464, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195204109, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xeueskr25s7x.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195205180, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195205245, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/LitJson_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195205315, "dur": 283, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/LitJson_CodeGen.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195205724, "dur": 98, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195205244, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/771r2rv7dn9h.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195205839, "dur": 773, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195206612, "dur": 724, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericAdjustorThunkTable.c"}}, {"pid": 12345, "tid": 3, "ts": 1752432195205839, "dur": 1560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dz7zyh4uv72h.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195208818, "dur": 309212, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/dz7zyh4uv72h.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195518084, "dur": 361, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__2.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1752432195518450, "dur": 221, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195518077, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ibbjyuv3iaxx.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195518691, "dur": 148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__16.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1752432195518846, "dur": 225, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195518677, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j9hprva696vp.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195519084, "dur": 141, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__13.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1752432195519231, "dur": 238, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195519076, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/c6qwbx1c0sr7.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195519509, "dur": 139, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1752432195519655, "dur": 212, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195519490, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/y41k2d6bhqfl.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195519891, "dur": 125, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__4.cpp"}}, {"pid": 12345, "tid": 3, "ts": 1752432195520033, "dur": 422, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1752432195519872, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g3zdtpxtkbz5.o"}}, {"pid": 12345, "tid": 3, "ts": 1752432195520483, "dur": 5226057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195183600, "dur": 819, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195184422, "dur": 14116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195198540, "dur": 418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195198958, "dur": 1201, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestTextureModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195200349, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195200423, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195200210, "dur": 434, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195198538, "dur": 2107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/scxct0yc2wui.o"}}, {"pid": 12345, "tid": 4, "ts": 1752432195200645, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195200706, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195200786, "dur": 257, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195201306, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195201491, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195201214, "dur": 498, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195200705, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kg3j5q9k1dkb.o"}}, {"pid": 12345, "tid": 4, "ts": 1752432195201713, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195201801, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195201872, "dur": 403, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195202775, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195202903, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195202543, "dur": 463, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195201800, "dur": 1206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/z1v50ahxo7po.o"}}, {"pid": 12345, "tid": 4, "ts": 1752432195203006, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195203094, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195203182, "dur": 380, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195203664, "dur": 410, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195203093, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/b8rf4klsbqu7.o"}}, {"pid": 12345, "tid": 4, "ts": 1752432195204074, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195204165, "dur": 290, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Numerics_CodeGen.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195204862, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195204717, "dur": 466, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195204130, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/x7yed1r0zizx.o"}}, {"pid": 12345, "tid": 4, "ts": 1752432195205184, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195205273, "dur": 3570, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195208844, "dur": 4211, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppTypeDefinitions.c"}}, {"pid": 12345, "tid": 4, "ts": 1752432195213184, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 4, "ts": 1752432195213145, "dur": 197, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1752432195205272, "dur": 8070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qbqspp9s4oqb.o"}}, {"pid": 12345, "tid": 4, "ts": 1752432195213872, "dur": 931235, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/qbqspp9s4oqb.o"}}, {"pid": 12345, "tid": 4, "ts": 1752432196145171, "dur": 4601358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195183615, "dur": 811, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195184426, "dur": 14105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195198533, "dur": 430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195198963, "dur": 1166, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195200240, "dur": 316, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195198532, "dur": 2024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/s3w4wumvzn3k.o"}}, {"pid": 12345, "tid": 5, "ts": 1752432195200589, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195200662, "dur": 281, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195200947, "dur": 62, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 5, "ts": 1752432195201306, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195201498, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195201655, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195201173, "dur": 537, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195200588, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gvymwn7z61hg.o"}}, {"pid": 12345, "tid": 5, "ts": 1752432195201710, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195201787, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195201885, "dur": 566, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195202715, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195202631, "dur": 400, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195201786, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ukr1rpa18ybu.o"}}, {"pid": 12345, "tid": 5, "ts": 1752432195203032, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195203117, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195203206, "dur": 335, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195203675, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195203769, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-windowsruntime-types.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195203674, "dur": 429, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195203115, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3i9xy9xqlcf1.o"}}, {"pid": 12345, "tid": 5, "ts": 1752432195204103, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195204183, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195204266, "dur": 472, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data_CodeGen.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195204802, "dur": 61, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 5, "ts": 1752432195205105, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195205185, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 5, "ts": 1752432195204942, "dur": 446, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1752432195204182, "dur": 1206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9hssvpo2gb9i.o"}}, {"pid": 12345, "tid": 5, "ts": 1752432195205420, "dur": 2296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataUsage.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195207716, "dur": 1845, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataUsage.c"}}, {"pid": 12345, "tid": 5, "ts": 1752432195205419, "dur": 4200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/etvd97efe4n8.o"}}, {"pid": 12345, "tid": 5, "ts": 1752432195209833, "dur": 733226, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/etvd97efe4n8.o"}}, {"pid": 12345, "tid": 5, "ts": 1752432195943112, "dur": 4803382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195183675, "dur": 802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195184477, "dur": 14020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195198533, "dur": 423, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195198956, "dur": 1203, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/__Generated_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195200306, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195200305, "dur": 254, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195198531, "dur": 2028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7b3cq9p9hfoj.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195200602, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195200671, "dur": 471, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195201299, "dur": 318, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195200601, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ssa4k6ltplza.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195201618, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195201724, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195201791, "dur": 673, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195202786, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195203096, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195203267, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195202786, "dur": 580, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195201722, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/omcaaxky6qm1.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195203366, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195203456, "dur": 366, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195204207, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195204263, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195204324, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-sanitizers.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195204036, "dur": 423, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195203422, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2ckxri5ccgpa.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195204460, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195204557, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195204633, "dur": 377, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Configuration_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195205175, "dur": 269, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195204556, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5r6y2gcha4vr.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195205477, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataRegistration.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195205560, "dur": 176, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppMetadataRegistration.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195205773, "dur": 106, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195205475, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ipny880rb5j5.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195205919, "dur": 298, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/HOTween_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195206262, "dur": 191, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195205893, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/849nfo018w46.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195206499, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/634bdxiijk3i.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195206633, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ykz8l2uw77q9.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195206764, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/DOTweenPro_CodeGen.c"}}, {"pid": 12345, "tid": 6, "ts": 1752432195206763, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zuixlk04e0rp.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195206908, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/awq568may2rr.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195206991, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195207071, "dur": 581, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/__Generated.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195207667, "dur": 341, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195206989, "dur": 1020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vhw277frpm9q.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195208057, "dur": 218, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195208286, "dur": 169, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195208027, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/u8a2b9vlpz0m.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195208485, "dur": 140, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195208630, "dur": 328, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195208469, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nlcpt7ppp0vv.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195208995, "dur": 135, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__8.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195209140, "dur": 253, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195208970, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/me3opogzrzcx.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195209432, "dur": 190, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195209626, "dur": 353, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195209409, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/0qfkpzl3k9jy.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195210008, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195210076, "dur": 431, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195210516, "dur": 268, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195210007, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tnor90cabvfg.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195210874, "dur": 260, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__13.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195211144, "dur": 508, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195210834, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/q0e7ascl3bzz.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195211687, "dur": 122, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195211816, "dur": 465, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195211671, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h5bmjn1e29fs.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195212288, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195212371, "dur": 383, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195212761, "dur": 386, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195212287, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/snctoikil7qk.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195213204, "dur": 263, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195213475, "dur": 654, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195213168, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/enystrut2gde.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195214185, "dur": 479, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195214670, "dur": 254, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195214148, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/13h0i84ywbpt.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195214943, "dur": 197, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GridModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195215146, "dur": 483, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195214928, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bm8it5gsiqof.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195215692, "dur": 210, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195216199, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-tiny.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195216253, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tabledefs.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195215908, "dur": 520, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195215670, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gjvb9r5cttly.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195216438, "dur": 128, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AIModule.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195216929, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/WaitStatus-c-api.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195216572, "dur": 440, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195216433, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h1prq5s3d0c4.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195217067, "dur": 230, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195217430, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/PlatformEnvironment.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195217308, "dur": 406, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195217035, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bivbnbb95tla.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195217756, "dur": 181, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195217943, "dur": 192, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195217736, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tyhuiljs6bhy.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195218174, "dur": 126, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Registration.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195218304, "dur": 297, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195218150, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/boriyfj1e5ro.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195218610, "dur": 119, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195218820, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_WakeupFallbackStrategy.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195218737, "dur": 273, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195218605, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qg4l122usa8d.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195219023, "dur": 146, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.ResourceManager.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195219175, "dur": 246, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195219015, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yaygo5gduoyq.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195219478, "dur": 175, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__9.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195219659, "dur": 197, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195219464, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ax33vqqgahpx.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195219892, "dur": 320, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__5.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195220248, "dur": 204, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195219874, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ie9m0i3pxe9v.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195220481, "dur": 237, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195220886, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-cpp.hpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195220723, "dur": 718, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195220468, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vi11fesvmmzh.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195221458, "dur": 163, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195221639, "dur": 339, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195221453, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ayuny8657b5y.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195222031, "dur": 193, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__16.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195222227, "dur": 197, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195221996, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nwsqisrcqgo6.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195222464, "dur": 119, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__13.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195222590, "dur": 369, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195222446, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gt1x2tijg2jo.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195223016, "dur": 101, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__10.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195223122, "dur": 277, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195222980, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9pa5mv9p6nf6.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195223426, "dur": 131, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Runtime.Serialization.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195223561, "dur": 261, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195223404, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/106r97em0u63.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195223835, "dur": 150, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__8.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195224258, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/google/sparsehash/template_util.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195224413, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorState.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195223990, "dur": 517, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195223826, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5kn4xwk5vxjk.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195224507, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195224565, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195224618, "dur": 340, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195225017, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Compiler/CompilerEnvironmentClang.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195224962, "dur": 312, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195224564, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cu86h9ggftm3.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195225296, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195225363, "dur": 259, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195226032, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodCompare.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195225630, "dur": 551, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195225295, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/l9fvdcvdumm2.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195226242, "dur": 126, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195226415, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringViewUtils.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195226373, "dur": 384, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195226211, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/x7iypquy173u.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195226758, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195226875, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime__2.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195227391, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195227523, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/MemoryUtils.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195227133, "dur": 727, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195226850, "dur": 1010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/es15rj966m34.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195227869, "dur": 118, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195228056, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/KeyWrapper.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195228321, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/google/sparsehash/sparsetable.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195227992, "dur": 516, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195227864, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d6pt7kw8dfca.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195228526, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195228581, "dur": 204, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195229189, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextHash.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195229351, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195229411, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/Il2CppError.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195228797, "dur": 793, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195228525, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/62y0rmnf407r.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195229653, "dur": 565, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__7.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195230224, "dur": 246, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195229607, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ytfq9tkxxxtf.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195230483, "dur": 159, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195230649, "dur": 274, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195230475, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6al9dmx6kvde.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195230964, "dur": 230, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__20.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195231198, "dur": 375, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195230941, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pxqr9i1mv3g9.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195231589, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195231643, "dur": 418, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__16.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195232070, "dur": 385, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195231588, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/w18twstscrqe.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195232474, "dur": 251, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__14.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195232731, "dur": 284, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195232469, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/aelpw3yw50xf.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195233064, "dur": 189, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195233395, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_ErrorCode.h"}}, {"pid": 12345, "tid": 6, "ts": 1752432195233265, "dur": 388, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195233029, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/knx4dx2ae5kf.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195233678, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195233772, "dur": 166, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Mono.Security.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195233942, "dur": 222, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195233676, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3asjvclk9pye.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195234186, "dur": 126, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppInteropDataTable.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195234318, "dur": 247, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195234181, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dm8duyczhj88.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195234586, "dur": 157, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCFieldValuesTable.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195234750, "dur": 240, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195234570, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cxyii6rge1bq.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195235002, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195235096, "dur": 199, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195234993, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/umjdpuphyvok.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195235305, "dur": 136, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/HOTween.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195235446, "dur": 231, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195235301, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4s00of92122i.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195235712, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Ump.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195235806, "dur": 164, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195235697, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/j4gyssn0v1vi.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195235977, "dur": 97, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.iOS.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195236078, "dur": 170, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195235974, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cjhrz960kq1x.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195236257, "dur": 100, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.Core.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195236361, "dur": 233, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195236253, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/66m6zwx9woqi.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195236649, "dur": 107, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__8.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195236759, "dur": 163, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195236633, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/goaetv93wudz.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195236930, "dur": 224, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__72.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195237157, "dur": 147, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195236926, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pm6vb5qs5f82.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195237339, "dur": 87, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__70.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195237429, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195237308, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3yirbgwwypf4.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195237583, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__69.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195237672, "dur": 201, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195237579, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h6rmeoxvprtr.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195237885, "dur": 130, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__67.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195238049, "dur": 194, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195237879, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vi4hez2zi10s.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195238251, "dur": 124, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__65.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195238380, "dur": 256, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195238247, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9ugdea83bb8t.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195238652, "dur": 103, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__63.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195238758, "dur": 266, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195238640, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3z90irf95ykr.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195239040, "dur": 133, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__61.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195239180, "dur": 216, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195239035, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hcmwi2kdz8rs.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195239443, "dur": 149, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195239596, "dur": 172, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195239410, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/at31xch9qiqw.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195239804, "dur": 198, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__59.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195240006, "dur": 195, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195239775, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a4r3507enmuw.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195240246, "dur": 144, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__57.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195240393, "dur": 146, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195240206, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/og4y5bdjgw1x.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195240552, "dur": 123, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__56.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195240679, "dur": 147, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195240542, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/n8jilwwkcuki.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195240838, "dur": 108, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__54.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195240951, "dur": 165, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195240830, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hdd6pd4p6hxf.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241126, "dur": 94, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__52.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241223, "dur": 149, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195241119, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vpp1po9tzr4g.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241386, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__50.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241478, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195241379, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/911wpzeys1bi.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241636, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__49.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241728, "dur": 149, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195241629, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/n9ass7fxj3p5.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241887, "dur": 94, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__47.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195241985, "dur": 147, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195241883, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/he4fxon2dr22.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195242138, "dur": 94, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__45.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195242236, "dur": 218, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195242134, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vqgbxzs66093.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195242464, "dur": 209, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__43.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195242676, "dur": 167, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195242459, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/92s6waya0ah5.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195242852, "dur": 128, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__41.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195242985, "dur": 154, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195242847, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nf51wl3etko6.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195243146, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__40.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195243235, "dur": 203, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195243142, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2z1dhcphqwcn.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195243447, "dur": 157, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__39.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195243616, "dur": 320, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195243442, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g7a9jkqghyqa.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195243997, "dur": 301, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__36.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195244380, "dur": 351, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195243963, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/93jg4lhn7jms.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195244753, "dur": 193, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__33.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195244950, "dur": 171, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195244744, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/20snon8tx5ha.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195245142, "dur": 134, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__32.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195245285, "dur": 168, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195245125, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1s8637rvt30u.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195245453, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195245544, "dur": 175, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__30.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195245726, "dur": 228, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195245512, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vx1u8oz0nrds.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195245967, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__29.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195246077, "dur": 395, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195245959, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/94aqbx0zetrf.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195246486, "dur": 282, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__27.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195246773, "dur": 213, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195246476, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/nhnlb85372yg.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195246997, "dur": 122, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__24.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195247124, "dur": 178, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195246991, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gewsv9wayouy.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195247312, "dur": 87, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__22.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195247403, "dur": 146, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195247308, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/uq8nvk2ery1z.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195247555, "dur": 88, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__20.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195247646, "dur": 151, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195247551, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/82kjvv7ik880.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195247806, "dur": 86, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__19.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195247896, "dur": 159, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195247800, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/mauey38hbano.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195248077, "dur": 104, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__17.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195248184, "dur": 193, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195248072, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gfn22kgn5xzl.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195248391, "dur": 89, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__16.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195248484, "dur": 158, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195248382, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/f72li5zo1vi6.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195248671, "dur": 111, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__14.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195248786, "dur": 165, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195248656, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9bw9mm7tvjv3.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195248956, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__12.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195249214, "dur": 114, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__12.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195249332, "dur": 145, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195248955, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/no84mxcyot24.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195249493, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics__10.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195249582, "dur": 144, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195249482, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/10k0m8h2h395.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195249747, "dur": 85, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Generics.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195249836, "dur": 140, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195249729, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/an9c5z78aooy.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195250002, "dur": 203, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__8.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195250208, "dur": 193, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195249980, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xwlxz5ho0gfx.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195250421, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195250504, "dur": 148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__6.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195250663, "dur": 296, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195250420, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r1el4mptu4ru.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195251043, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195251198, "dur": 87, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__4.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195251288, "dur": 227, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195251041, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5drg4xuxnezv.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195251533, "dur": 249, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__3.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195251792, "dur": 227, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195251521, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/456zjiezjcig.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195252027, "dur": 109, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__15.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195252141, "dur": 215, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195252023, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/t78tf8f0gcf0.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195252368, "dur": 118, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__14.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195252491, "dur": 257, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195252359, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/szodvsz1cayk.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195252748, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195252898, "dur": 89, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__12.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195252991, "dur": 185, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195252887, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/m4h1z9666xai.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195253177, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195253299, "dur": 168, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__10.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195253471, "dur": 148, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195253261, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/0guwzkcb07hj.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195253632, "dur": 131, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods__1.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195253766, "dur": 261, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195253623, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ya0nozl4d0ud.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195254033, "dur": 3932, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195257965, "dur": 1300, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GenericMethods.cpp"}}, {"pid": 12345, "tid": 6, "ts": 1752432195259268, "dur": 136, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1752432195254032, "dur": 5372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hl273qhjh8by.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432195260266, "dur": 2243274, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/hl273qhjh8by.o"}}, {"pid": 12345, "tid": 6, "ts": 1752432197503614, "dur": 3242853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195183664, "dur": 795, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195184459, "dur": 14035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195198539, "dur": 430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195198969, "dur": 1174, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195200245, "dur": 329, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195198538, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rlveio9iupj5.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195200575, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195200655, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195200732, "dur": 447, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195201307, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195201256, "dur": 512, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195200654, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a1rlmtf9gapa.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195201769, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195201849, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195201932, "dur": 334, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.GameCenterModule_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195202394, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195202500, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195202674, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195202393, "dur": 494, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195201848, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gyb9b3t4kute.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195202919, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195203004, "dur": 503, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195203532, "dur": 56, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195202918, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ewtqdnhc7z8h.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195203633, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.ResourceManager_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195203722, "dur": 207, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.ResourceManager_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195204081, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195204206, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-runtime-metadata.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195204264, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/pch/pch-c.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195204350, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 7, "ts": 1752432195204074, "dur": 462, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195203632, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ecb4eajx7p87.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195204536, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195204613, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195204740, "dur": 367, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime_CodeGen.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195205379, "dur": 270, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195204612, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yd2ouo78rctn.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195205650, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195205721, "dur": 604, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodDefinitions.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195206325, "dur": 1015, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodDefinitions.c"}}, {"pid": 12345, "tid": 7, "ts": 1752432195205720, "dur": 1666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2c8vrpjsa9ps.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195208815, "dur": 309240, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/2c8vrpjsa9ps.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195518253, "dur": 3125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__19.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1752432195521378, "dur": 1158, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__19.cpp"}}, {"pid": 12345, "tid": 7, "ts": 1752432195522544, "dur": 261, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1752432195518252, "dur": 4553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qc9h7tjzjl07.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432195523266, "dur": 1140588, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/qc9h7tjzjl07.o"}}, {"pid": 12345, "tid": 7, "ts": 1752432196663925, "dur": 4082617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195183643, "dur": 787, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195184431, "dur": 14108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195198541, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195198975, "dur": 1178, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195200212, "dur": 329, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195198539, "dur": 2002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lqglya41701f.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195200542, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195200631, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195200694, "dur": 447, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195201359, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195201655, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195201218, "dur": 610, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195200630, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pcykjd1h8f9e.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195201829, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195201924, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195202015, "dur": 389, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195202720, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195202775, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195202899, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195202720, "dur": 645, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195201923, "dur": 1442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/36hkdb79ao9i.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195203393, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195203495, "dur": 61, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195203866, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195203576, "dur": 351, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195203392, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/swmbrszops7b.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195203968, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195204055, "dur": 233, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195204296, "dur": 61, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 8, "ts": 1752432195204861, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195204463, "dur": 454, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195203967, "dur": 950, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/e8g7udxfo4ml.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195204955, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195205017, "dur": 373, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib_CodeGen.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195205476, "dur": 202, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195204955, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bywteccudb2n.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195205702, "dur": 4495, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodPointerTable.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195210199, "dur": 3930, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericMethodPointerTable.c"}}, {"pid": 12345, "tid": 8, "ts": 1752432195214250, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 8, "ts": 1752432195214151, "dur": 192, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1752432195205701, "dur": 8642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/x6f6us6dovc4.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195215190, "dur": 610939, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/x6f6us6dovc4.o"}}, {"pid": 12345, "tid": 8, "ts": 1752432195826185, "dur": 4920297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195183664, "dur": 801, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195184465, "dur": 14076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195198543, "dur": 422, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195198965, "dur": 1172, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195200246, "dur": 69, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool"}}, {"pid": 12345, "tid": 9, "ts": 1752432195200467, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195200749, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-sanitizers.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195200466, "dur": 477, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195198541, "dur": 2403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zoychesvr17b.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195200945, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195201066, "dur": 326, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195201696, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195202030, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-sanitizers.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195202181, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195202234, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-platforms.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195201577, "dur": 749, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195201039, "dur": 1287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6rcy9ehx2zou.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195202326, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195202467, "dur": 475, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AIModule_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195203272, "dur": 299, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195202445, "dur": 1127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xqfjqzo5tdlj.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195203573, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195203684, "dur": 396, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Addressables_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195204408, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-sanitizers.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195204286, "dur": 384, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195203659, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kad144hmoeb4.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195204671, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195204787, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Examples_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195204856, "dur": 321, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Examples_CodeGen.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195205213, "dur": 51, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/libtool"}}, {"pid": 12345, "tid": 9, "ts": 1752432195205379, "dur": 318, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195204786, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1fkcfyitb1z2.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195205739, "dur": 1320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericInstDefinitions.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195207060, "dur": 1213, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericInstDefinitions.c"}}, {"pid": 12345, "tid": 9, "ts": 1752432195205738, "dur": 2573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ljifdvr3lhyx.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195208830, "dur": 309300, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/ljifdvr3lhyx.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195518301, "dur": 150, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__17.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1752432195518456, "dur": 219, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195518290, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4old74o3cv78.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195518701, "dur": 146, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__15.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1752432195518853, "dur": 228, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195518680, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/i1x86fu754ea.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195519110, "dur": 97, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__11.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1752432195519214, "dur": 214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195519096, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qi3rb86gt2y8.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195519439, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1752432195519694, "dur": 320, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195519433, "dur": 581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h3q2dff5e8fi.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195520034, "dur": 112, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__2.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1752432195520149, "dur": 216, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195520027, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/a8s1x64yezb3.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195520413, "dur": 110, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__1.cpp"}}, {"pid": 12345, "tid": 9, "ts": 1752432195520609, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tabledefs.h"}}, {"pid": 12345, "tid": 9, "ts": 1752432195520527, "dur": 243, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1752432195520396, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9z8kdro0axun.o"}}, {"pid": 12345, "tid": 9, "ts": 1752432195520774, "dur": 5225809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195183666, "dur": 805, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195184471, "dur": 14035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195198517, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195198697, "dur": 1467, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195200210, "dur": 64, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 10, "ts": 1752432195200339, "dur": 357, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195198515, "dur": 2181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/f1nvxdn4ro8h.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195200697, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195200808, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195200888, "dur": 333, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195201310, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/StringView.h"}}, {"pid": 12345, "tid": 10, "ts": 1752432195201309, "dur": 456, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195200807, "dur": 959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/6fqb5n7tjyax.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195201766, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195201863, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195201964, "dur": 534, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195202672, "dur": 494, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195201862, "dur": 1304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/f98f88xtjdd8.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195203166, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195203295, "dur": 257, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195203984, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 10, "ts": 1752432195203766, "dur": 367, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195203263, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jyczhld27ain.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195204177, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Drawing_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195204242, "dur": 287, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Drawing_CodeGen.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195204863, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 10, "ts": 1752432195204990, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-metadata.h"}}, {"pid": 12345, "tid": 10, "ts": 1752432195204715, "dur": 496, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195204175, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9029gcwjqef2.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195205212, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195205284, "dur": 356, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195205640, "dur": 1065, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppRgctxTable.c"}}, {"pid": 12345, "tid": 10, "ts": 1752432195206823, "dur": 65, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 10, "ts": 1752432195206922, "dur": 68, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 10, "ts": 1752432195206990, "dur": 63, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195205283, "dur": 1771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/iieotj7rc42k.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195208808, "dur": 309241, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/iieotj7rc42k.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195518280, "dur": 172, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__18.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1752432195518458, "dur": 235, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195518257, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5w5trk51gxoo.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195518708, "dur": 139, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__14.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1752432195518852, "dur": 229, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195518696, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xlukr6fa3g3q.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195519114, "dur": 90, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__12.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1752432195519210, "dur": 214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195519088, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/by6frhlewqas.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195519439, "dur": 237, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp__10.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1752432195519681, "dur": 270, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195519428, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/52z3vysjqenp.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195519975, "dur": 244, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Assembly-CSharp-firstpass__3.cpp"}}, {"pid": 12345, "tid": 10, "ts": 1752432195520224, "dur": 284, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1752432195519962, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vnwpdgjwhnnm.o"}}, {"pid": 12345, "tid": 10, "ts": 1752432195520511, "dur": 5225954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195183675, "dur": 808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195184483, "dur": 14060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195198545, "dur": 431, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195198976, "dur": 1170, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityAnalyticsModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195200295, "dur": 210, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195198544, "dur": 1961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/edtcvp589yg8.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195200545, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195200644, "dur": 404, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195201218, "dur": 559, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195200544, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7cxizvta6dws.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195201778, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195201854, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195201937, "dur": 422, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195202392, "dur": 54, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}}, {"pid": 12345, "tid": 11, "ts": 1752432195202495, "dur": 477, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195201853, "dur": 1119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/gg2l0vnczivb.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195202972, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195203051, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195203149, "dur": 349, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Telemetry_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195203580, "dur": 282, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195203050, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/lpgfwsh2x8sl.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195203890, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195203981, "dur": 320, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195204458, "dur": 67, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 11, "ts": 1752432195204989, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/c-api/il2cpp-config-api-platforms.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195204525, "dur": 519, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195203889, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d45tq6dfbt6r.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195205076, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195205136, "dur": 342, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Mono.Security_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195205733, "dur": 272, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195205075, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bef37b0now80.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195206053, "dur": 104, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195206283, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195206020, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/plbfolwvql7u.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195206557, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/anvaf2y03061.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195206657, "dur": 208, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/FairyGUI_CodeGen.c"}}, {"pid": 12345, "tid": 11, "ts": 1752432195206911, "dur": 144, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195206639, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/08mmtqiteetm.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195207056, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195207133, "dur": 321, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UniWebView-CSharp.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195207458, "dur": 305, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195207123, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7l7wgc3tgqoc.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195207829, "dur": 364, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195208196, "dur": 193, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195207811, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h62x2rbjnavj.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195208403, "dur": 188, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI__3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195208601, "dur": 312, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195208393, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/otw5dn6nt2cb.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195208924, "dur": 288, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__9.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195209222, "dur": 440, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195208917, "dur": 747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7t6c5xuwunog.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195209698, "dur": 143, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195209990, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/vm/MarshalAlloc.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195210047, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/google/sparsehash/template_util.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195209847, "dur": 531, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195209669, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/uv87tgt8exwv.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195210431, "dur": 220, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__14.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195210986, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Platforms/IOS/Include/C/Baselib_ErrorState.inl.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195210660, "dur": 577, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195210390, "dur": 847, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bgivq1qi6zaj.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195211237, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195211398, "dur": 177, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__10.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195211582, "dur": 241, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195211391, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/346lqn1qsjpl.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195211844, "dur": 129, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TilemapModule.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195211978, "dur": 238, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195211831, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/zxwonr2aadgn.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195212248, "dur": 484, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195212740, "dur": 318, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195212229, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/d3fh4ryfov23.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195213124, "dur": 388, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.SpriteShapeModule.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195213951, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Cpp/ReentrantLock.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195213521, "dur": 548, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195213095, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/p7q9nffgopw1.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195214088, "dur": 252, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ParticleSystemModule.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195214520, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/vm/Type.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195214351, "dur": 476, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195214074, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ywp150jo34u1.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195214892, "dur": 247, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.ImageConversionModule.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195215144, "dur": 276, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195214850, "dur": 570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2d6qv5nj9z79.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195215455, "dur": 207, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.CoreModule__2.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195215668, "dur": 346, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195215434, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/04x4ouq1akp2.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195216041, "dur": 117, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AudioModule.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195216163, "dur": 422, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195216019, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/fc8zqdsmo4a6.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195216626, "dur": 214, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__6.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195216852, "dur": 308, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195216619, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yd3w7fm3gmvo.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195217214, "dur": 131, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__2.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195217350, "dur": 287, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195217188, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/q2rn71wb359q.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195217673, "dur": 293, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Threading.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195217973, "dur": 354, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195217653, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/00wn3u0nqe6t.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195218370, "dur": 358, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Environments.Internal.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195218824, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/codegen/il2cpp-codegen-metadata.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195218734, "dur": 381, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195218342, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/77g6tx0eqv91.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195219125, "dur": 136, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Addressables__1.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195219266, "dur": 307, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195219119, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jdj8mpvdwc6b.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195219582, "dur": 138, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__7.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195219724, "dur": 227, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195219577, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/o9fzv1vk3qwy.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195219990, "dur": 301, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195220296, "dur": 223, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195219972, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h6o6f3mrtcsg.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195220538, "dur": 130, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__10.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195220672, "dur": 180, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195220529, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/sv0y57ui6wtm.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195220891, "dur": 111, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__9.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195221007, "dur": 545, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195220876, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/h1lge7fzhq9g.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195221582, "dur": 109, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195221695, "dur": 307, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195221577, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oa7iyjca1lc0.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195222011, "dur": 253, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__15.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195222269, "dur": 438, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195222006, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/mo89xcbeme7q.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195222724, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__12.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195222780, "dur": 141, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__12.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195222925, "dur": 312, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195222723, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/flhhie2kc028.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195223260, "dur": 151, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml.Linq.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195223437, "dur": 242, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195223244, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/e7aqp4yswmsv.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195223723, "dur": 229, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Drawing.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195223957, "dur": 199, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195223704, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xyd7wr0fkxjr.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195224164, "dur": 524, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__6.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195224693, "dur": 204, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195224159, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jxzzx7p9o7ql.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195224969, "dur": 429, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__2.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195225421, "dur": 333, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195224946, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bmoqxt0hbr5n.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195225765, "dur": 107, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core__3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195226021, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/BasicTypes.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195225877, "dur": 526, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195225758, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/jv62pfpu8xwt.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195226430, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195226509, "dur": 427, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195227033, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/Memory.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195226944, "dur": 416, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195226429, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/is7lihp4nd14.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195227410, "dur": 231, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Runtime.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195227726, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericContextCompare.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195227649, "dur": 399, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195227383, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/is19zuh5iffl.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195228066, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195228147, "dur": 172, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json__3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195228535, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-config-api.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195228676, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tabledefs.h"}}, {"pid": 12345, "tid": 11, "ts": 1752432195228326, "dur": 514, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195228065, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/sqm5rbibbr0q.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195228850, "dur": 1198, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__9.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195230128, "dur": 241, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195228845, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/kh3u9ift4nme.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195230384, "dur": 175, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__4.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195230572, "dur": 247, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195230373, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/rqoxtvb4njpx.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195230844, "dur": 250, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__21.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195231099, "dur": 203, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195230824, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/adufpsfkyrss.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195231340, "dur": 91, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__19.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195231436, "dur": 638, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195231320, "dur": 754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/34zmcr2lm5uw.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195232118, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195232175, "dur": 160, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__15.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195232340, "dur": 235, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195232117, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/bm55gjfv92ev.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195232586, "dur": 188, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__11.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195232780, "dur": 382, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195232578, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3auwg5p3vmtx.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195233236, "dur": 559, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195233852, "dur": 216, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195233207, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/n6vq511fh55t.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195234088, "dur": 142, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195234238, "dur": 242, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195234073, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r5y0o9v15oc2.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195234510, "dur": 162, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCTypeValuesTable.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195234677, "dur": 214, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195234496, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/qqy3g6x7x8rm.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195234899, "dur": 117, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues3.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195235020, "dur": 188, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195234895, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ga7hpjkd5lhj.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195235214, "dur": 2289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195237503, "dur": 1273, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateFieldValues.cpp"}}, {"pid": 12345, "tid": 11, "ts": 1752432195238780, "dur": 264, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1752432195235213, "dur": 3831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5swo98na0ly8.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195239883, "dur": 684272, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/5swo98na0ly8.o"}}, {"pid": 12345, "tid": 11, "ts": 1752432195924223, "dur": 4822263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195183684, "dur": 801, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195184485, "dur": 14087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195198573, "dur": 397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195198970, "dur": 1151, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UI_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195200209, "dur": 251, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195198572, "dur": 1888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/djv1nd5dzmrp.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195200492, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195200554, "dur": 336, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195200986, "dur": 55, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 12, "ts": 1752432195201312, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-blob.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195201041, "dur": 350, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195200490, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cwsu6mgj6qlu.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195201391, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195201461, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195201569, "dur": 400, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195202176, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195202173, "dur": 538, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195201460, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/9ev8palispau.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195202711, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195202803, "dur": 515, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195203457, "dur": 71, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/dummy"}}, {"pid": 12345, "tid": 12, "ts": 1752432195203528, "dur": 332, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195202769, "dur": 1091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/m3fnl85zogdf.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195203860, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195203956, "dur": 424, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195204404, "dur": 403, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195203924, "dur": 883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/2k89zmkqzr4x.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195204807, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195204888, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195204947, "dur": 439, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195205397, "dur": 299, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195204887, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dlrjy2le14oc.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195205696, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195205794, "dur": 218, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppGenericClassTable.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195206137, "dur": 330, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195205760, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/p93rmqbe7knl.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195206547, "dur": 50, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/GoogleMobileAds.iOS_CodeGen.c"}}, {"pid": 12345, "tid": 12, "ts": 1752432195206624, "dur": 70, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195206507, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/opbh2m4jpvzg.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195206818, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-tokentype.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195206919, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-class-internals.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195207007, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-api-types.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195207076, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-pinvoke-support.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195206783, "dur": 374, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195206721, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cfbwa8ntkv2f.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195207245, "dur": 226, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestWWWModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195207477, "dur": 456, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195207220, "dur": 714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/sy76hjmf7kol.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195207958, "dur": 464, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UnityWebRequestAssetBundleModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195208428, "dur": 325, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195207952, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/r1ju5nx35m1o.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195208793, "dur": 258, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195209055, "dur": 221, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195208764, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/179p6ggeadv1.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195209322, "dur": 366, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__7.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195209991, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/utils/MemoryUtils.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195210053, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppSignature.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195209718, "dur": 466, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195209312, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/1yz09e21ob1d.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195210218, "dur": 254, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__2.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195210710, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/CoreMacros.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195210479, "dur": 342, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195210191, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/87k3tryc863w.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195210869, "dur": 108, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__12.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195210985, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/vm-utils/Debugger.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195210983, "dur": 413, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195210839, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/psuqqcwmz9hk.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195211406, "dur": 158, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.UIElementsModule__1.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195211572, "dur": 277, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195211401, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7z0m9bie44mh.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195211876, "dur": 136, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextRenderingModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195212419, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/il2cpp-object-internals.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195212016, "dur": 510, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195211862, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cnox8xildtgu.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195212546, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195212611, "dur": 166, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195212784, "dur": 466, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195212544, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/0lll9yhi6hoi.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195213299, "dur": 157, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PropertiesModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195213464, "dur": 256, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195213273, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4m0gielit5uy.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195213750, "dur": 311, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.PhysicsModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195214263, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppTypeCompare.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195214520, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/CoreMacros.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195214067, "dur": 548, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195213735, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/4efzp9rzyi6p.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195214635, "dur": 428, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.IMGUIModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195215070, "dur": 298, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195214620, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/wchd43db5550.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195215403, "dur": 354, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195216033, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Compiler/CompilerEnvironmentGcc.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195215768, "dur": 390, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195215378, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/b4za84dd8v2y.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195216204, "dur": 244, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/UnityEngine.AssetBundleModule.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195216457, "dur": 344, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195216182, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/7ghueblini0n.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195216848, "dur": 152, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__5.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195217198, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/C/Baselib_CappedSemaphore.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195217016, "dur": 393, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195216825, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/x5ign054cke9.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195217443, "dur": 105, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.TextMeshPro__1.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195217553, "dur": 285, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195217418, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/5mozrsie0hy7.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195217877, "dur": 128, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Scheduler.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195218363, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/google/sparsehash/internal/hashtable-common.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195218009, "dur": 426, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195217859, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3831h4bskz8s.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195218449, "dur": 130, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Core.Device.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195218586, "dur": 224, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195218440, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/8boinpahjrlk.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195218839, "dur": 111, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Services.Analytics.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195218956, "dur": 437, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195218815, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/dn97iqvabql0.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195219447, "dur": 164, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Unity.Addressables.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195219617, "dur": 204, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195219407, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cvmrntd52sss.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195219829, "dur": 273, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__6.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195220109, "dur": 398, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195219825, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/3tcbgshn02kf.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195220516, "dur": 259, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System__11.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195220780, "dur": 350, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195220512, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/db4mlh9f9k55.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195221172, "dur": 100, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__7.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195221279, "dur": 241, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195221146, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/vdybeil3a0gi.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195221540, "dur": 185, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__5.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195221729, "dur": 249, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195221524, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pirzizs84ntf.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195221983, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195222063, "dur": 415, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__2.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195222521, "dur": 349, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195221982, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/if0620jfv9ox.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195222900, "dur": 156, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml__11.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195223060, "dur": 269, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195222874, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/u5dt24nn9brp.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195223356, "dur": 175, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Xml.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195224064, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericMethodCompare.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195223575, "dur": 634, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195223335, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/66e1ruuw56aa.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195224241, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__5.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195224320, "dur": 395, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data__5.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195224721, "dur": 282, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195224239, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yhwbiybcmjf2.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195225037, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195225112, "dur": 301, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Data.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195225434, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/gc/WriteBarrier.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195225689, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/external/baselib/Include/Internal/Compiler/CompilerEnvironmentMsvc.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195225421, "dur": 568, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195225036, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/tkszcqhoh0px.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195226035, "dur": 118, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Core__2.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195226410, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/os/Win32/ThreadLocalValueImpl.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195226159, "dur": 499, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195226011, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/yf3ea6ax59ka.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195226708, "dur": 362, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/System.Configuration.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195227112, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/metadata/Il2CppGenericInstCompare.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195227077, "dur": 383, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195226678, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/99jst3w5yfgx.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195227500, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Examples.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195227567, "dur": 667, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/ProCamera2D.Examples.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195228242, "dur": 400, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195227498, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/ppcpo0wpchk7.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195228683, "dur": 143, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Newtonsoft.Json.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195229183, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/IL2CPP/libil2cpp/vm/GenericClass.h"}}, {"pid": 12345, "tid": 12, "ts": 1752432195228834, "dur": 610, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195228662, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/pdawpui3dglk.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195229516, "dur": 622, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__8.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195230142, "dur": 207, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195229482, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/z1z6t90v1zav.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195230371, "dur": 164, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__5.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195230541, "dur": 207, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195230365, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/sy8debs2rl6c.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195230770, "dur": 379, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__22.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195231153, "dur": 297, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195230765, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/blew97vi2t97.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195231523, "dur": 700, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__17.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195232229, "dur": 338, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195231492, "dur": 1075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/hhcic27pff2x.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195232583, "dur": 187, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/mscorlib__12.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195232777, "dur": 468, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195232574, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/oqxkwe40ya5g.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195233261, "dur": 386, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Mono.Security__1.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195233651, "dur": 408, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195233249, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/g41jlm5i7cwn.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195234070, "dur": 148, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195234224, "dur": 240, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195234065, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/cfjjf3rzd6gq.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195234473, "dur": 169, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCodeRegistration.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195234648, "dur": 200, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195234468, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/43q3vcuussa1.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195234852, "dur": 3041, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195237893, "dur": 1717, "ph": "X", "name": "ScanImplicitDeps", "args": {"detail": "/Users/<USER>/Game_Projects/KidsAppsCenterProject/KidsLearningSuite/AppsProjects/GameApp/IosApp202507/Il2CppOutputProject/Source/il2cppOutput/Il2CppCCalculateTypeValues.cpp"}}, {"pid": 12345, "tid": 12, "ts": 1752432195239615, "dur": 213, "ph": "X", "name": "ImplicitDepsSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1752432195234851, "dur": 4977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "C_iOS_arm64 z7ln/xrnzvvu4ikst.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195240392, "dur": 684320, "ph": "X", "name": "C_iOS_arm64", "args": {"detail": "z7ln/xrnzvvu4ikst.o"}}, {"pid": 12345, "tid": 12, "ts": 1752432195924748, "dur": 4821806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1752432200757068, "dur": 919, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20369, "tid": 1, "ts": 1752432200769876, "dur": 612, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend_profiler0.traceevents"}}, {"pid": 20369, "tid": 1, "ts": 1752432200770496, "dur": 377, "ph": "X", "name": "backend_profiler0.traceevents", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432200768175, "dur": 3325, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"pid": 20369, "tid": 1, "ts": 1752432200795858, "dur": 2992, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}